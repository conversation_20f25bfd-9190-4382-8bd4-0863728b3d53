{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/job-portal/client/src/app/font-test/page.tsx"], "sourcesContent": ["export default function FontTestPage() {\n  return (\n    <div className=\"container mx-auto px-4 py-8 space-y-8\">\n      <div className=\"max-w-4xl mx-auto\">\n        <h1 className=\"mb-8\">Font Implementation Test</h1>\n        \n        <div className=\"space-y-12\">\n          {/* Headings Section */}\n          <section className=\"space-y-4\">\n            <h2 className=\"text-2xl font-semibold mb-4\">Headings (Urbanist Font)</h2>\n            <div className=\"space-y-4\">\n              <h1>This is an H1 heading using Urbanist font with text-3xl</h1>\n              <h1 className=\"text-4xl\">This is a larger H1 with text-4xl</h1>\n              <h1 className=\"text-2xl\">This is a smaller H1 with text-2xl</h1>\n            </div>\n          </section>\n\n          {/* Paragraphs Section */}\n          <section className=\"space-y-4\">\n            <h2 className=\"text-2xl font-semibold mb-4\">Paragraphs (Inter Font)</h2>\n            <div className=\"space-y-4\">\n              <p>\n                This is a paragraph using Inter font with text-base. Lorem ipsum dolor sit amet, \n                consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore \n                magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.\n              </p>\n              <p className=\"text-lg\">\n                This is a larger paragraph with text-lg. Duis aute irure dolor in reprehenderit \n                in voluptate velit esse cillum dolore eu fugiat nulla pariatur.\n              </p>\n              <p className=\"text-sm\">\n                This is a smaller paragraph with text-sm. Excepteur sint occaecat cupidatat non \n                proident, sunt in culpa qui officia deserunt mollit anim id est laborum.\n              </p>\n            </div>\n          </section>\n\n          {/* Code and Labels Section */}\n          <section className=\"space-y-4\">\n            <h2 className=\"text-2xl font-semibold mb-4\">Code & Labels (JetBrains Mono Font)</h2>\n            <div className=\"space-y-4\">\n              <div>\n                <label htmlFor=\"test-input\" className=\"block mb-2\">\n                  This is a label using JetBrains Mono with text-xs\n                </label>\n                <input \n                  id=\"test-input\" \n                  type=\"text\" \n                  className=\"border rounded px-3 py-2\" \n                  placeholder=\"Input field\"\n                />\n              </div>\n              \n              <div>\n                <p className=\"mb-2\">Inline code example:</p>\n                <p>Here is some <code className=\"bg-gray-100 px-2 py-1 rounded\">inline code</code> in a sentence.</p>\n              </div>\n              \n              <div>\n                <p className=\"mb-2\">Code block example:</p>\n                <pre className=\"bg-gray-100 p-4 rounded overflow-x-auto\">\n                  <code>{`function example() {\n  console.log(\"This is JetBrains Mono font\");\n  return \"Hello World\";\n}`}</code>\n                </pre>\n              </div>\n            </div>\n          </section>\n\n          {/* Manual Font Classes Section */}\n          <section className=\"space-y-4\">\n            <h2 className=\"text-2xl font-semibold mb-4\">Manual Font Classes</h2>\n            <div className=\"space-y-4\">\n              <div>\n                <p className=\"mb-2\">Using font-heading class manually:</p>\n                <p className=\"font-heading text-2xl\">This text uses font-heading (Urbanist)</p>\n              </div>\n              \n              <div>\n                <p className=\"mb-2\">Using font-sans class manually:</p>\n                <p className=\"font-sans text-lg\">This text uses font-sans (Inter)</p>\n              </div>\n              \n              <div>\n                <p className=\"mb-2\">Using font-mono class manually:</p>\n                <p className=\"font-mono text-base\">This text uses font-mono (JetBrains Mono)</p>\n              </div>\n            </div>\n          </section>\n\n          {/* Mixed Content Example */}\n          <section className=\"space-y-4\">\n            <h2 className=\"text-2xl font-semibold mb-4\">Mixed Content Example</h2>\n            <div className=\"bg-gray-50 p-6 rounded-lg space-y-4\">\n              <h1>Welcome to Our Job Portal</h1>\n              <p>\n                Find your dream job with our advanced search features. We connect talented \n                professionals with amazing opportunities across various industries.\n              </p>\n              <div className=\"space-y-2\">\n                <label htmlFor=\"job-search\">Search for jobs:</label>\n                <input \n                  id=\"job-search\" \n                  type=\"text\" \n                  className=\"w-full border rounded px-3 py-2\" \n                  placeholder=\"e.g. Software Engineer\"\n                />\n              </div>\n              <p>\n                Use our <code>advanced filters</code> to narrow down your search results \n                and find the perfect match for your skills and preferences.\n              </p>\n            </div>\n          </section>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAO;;;;;;8BAErB,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAQ,WAAU;;8CACjB,8OAAC;oCAAG,WAAU;8CAA8B;;;;;;8CAC5C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;4CAAG,WAAU;sDAAW;;;;;;sDACzB,8OAAC;4CAAG,WAAU;sDAAW;;;;;;;;;;;;;;;;;;sCAK7B,8OAAC;4BAAQ,WAAU;;8CACjB,8OAAC;oCAAG,WAAU;8CAA8B;;;;;;8CAC5C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAE;;;;;;sDAKH,8OAAC;4CAAE,WAAU;sDAAU;;;;;;sDAIvB,8OAAC;4CAAE,WAAU;sDAAU;;;;;;;;;;;;;;;;;;sCAQ3B,8OAAC;4BAAQ,WAAU;;8CACjB,8OAAC;oCAAG,WAAU;8CAA8B;;;;;;8CAC5C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAa,WAAU;8DAAa;;;;;;8DAGnD,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAO;;;;;;8DACpB,8OAAC;;wDAAE;sEAAa,8OAAC;4DAAK,WAAU;sEAAgC;;;;;;wDAAkB;;;;;;;;;;;;;sDAGpF,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAO;;;;;;8DACpB,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;kEAAM,CAAC;;;CAGzB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOQ,8OAAC;4BAAQ,WAAU;;8CACjB,8OAAC;oCAAG,WAAU;8CAA8B;;;;;;8CAC5C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAO;;;;;;8DACpB,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAGvC,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAO;;;;;;8DACpB,8OAAC;oDAAE,WAAU;8DAAoB;;;;;;;;;;;;sDAGnC,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAO;;;;;;8DACpB,8OAAC;oDAAE,WAAU;8DAAsB;;;;;;;;;;;;;;;;;;;;;;;;sCAMzC,8OAAC;4BAAQ,WAAU;;8CACjB,8OAAC;oCAAG,WAAU;8CAA8B;;;;;;8CAC5C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAE;;;;;;sDAIH,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,SAAQ;8DAAa;;;;;;8DAC5B,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAGhB,8OAAC;;gDAAE;8DACO,8OAAC;8DAAK;;;;;;gDAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrD", "debugId": null}}, {"offset": {"line": 498, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/job-portal/client/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 536, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/job-portal/client/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,EAAwB;gBAAsB,EAAC,UAAA;oBAAA;oBAAA,CAEzG;oBAAA,yDAA4D;wBAC5D,KAAO,KAAA,CAAMC;wBAAAA,QAAc;4BAAA,GAAIX,CAAAA,gBAAmB;4BAAA;yBAAA;;mBAChDY,YAAY;;iBACVC,MAAMZ,UAAUa,QAAQ;sBACxBC,IAAAA,CAAM,CAAA;YAAA;SAAA;;SACNC,UAAU;cACV,IAAA;YAAA,MAAA,4BAA2C;iBAC3CC,MAAAA,MAAY,EAAA;wBAAA;4BACZC,KAAAA,CAAAA,GAAAA,EAAU,0MAAVA,CAAAA,sBAAU,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACVC,OAAAA,iTAAU,EAAE,QAAA,CAAA,KAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BACd,MAAA,CAAA,YAAA,CAAA;wBACAC;qBAAAA,MAAU;gBACRC,YAAYnB;UACd;QAAA,UAAA;YAAA,IAAA;YAAA;SAAA;QACF,CAAE,YAAA;YAAA,IAAA;YAAA;SAAA", "ignoreList": [0], "debugId": null}}]}