"use client"

import React, { useState } from "react"
import Link from "next/link"
// import { motion } from "framer-motion"
import { useAuth } from "@/hooks/use-auth"
import { useMutation, useQueryClient } from "@tanstack/react-query"
import {
  <PERSON><PERSON>,
} from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import {
  MapPin,
  Clock,
  DollarSign,
  Calendar,
  ArrowLeft,
  ExternalLink,
  Loader2,
  Flame,
  FileText,
  Target,
  Rocket,
  Lightbulb
} from "lucide-react"
import {
  Job,
  api,
  formatSalary,
  getJobTypeLabel,
  getExperienceLabel,
  getWorkTypeLabel,
  formatDate,
} from "@/lib/api"
import { toast } from "sonner"

interface JobDetailsClientProps {
  job: Job
  hasApplied: boolean
}

export function JobDetailsClient({ job, hasApplied }: JobDetailsClientProps) {
  const { user, isAuthenticated } = useAuth()
  const [coverLetter, setCoverLetter] = useState("")
  const [isApplyDialogOpen, setIsApplyDialogOpen] = useState(false)
  const [isProfileIncompleteAlertOpen, setIsProfileIncompleteAlertOpen] = useState(false)
  const [isUpdateProfileAlertOpen, setIsUpdateProfileAlertOpen] = useState(false)
  const queryClient = useQueryClient()

  const applyMutation = useMutation({
    mutationFn: (cover: string) => api.applyToJob(job.id, cover),
    onSuccess: (data) => {
      toast.success(data.message || "Application submitted successfully!")
      setIsApplyDialogOpen(false)
      setCoverLetter("")
      queryClient.invalidateQueries({ queryKey: ["my-applications"] })
    },
    onError: (error) => {
      toast.error(
        error instanceof Error ? error.message : "Failed to submit application"
      )
    },
  })

  const checkProfileCompletion = () => {
    if (!isAuthenticated) {
      toast.error("Please sign in to apply for jobs")
      return false
    }

    if (user?.profileCompleted === true) {
      return true
    }

    setIsProfileIncompleteAlertOpen(true)
    return false
  }

  const handleApplyClick = () => {
    if (checkProfileCompletion()) {
      // Ask if they want to update their profile before applying
      setIsUpdateProfileAlertOpen(true)
    }
  }

  const handleApply = () => {
    applyMutation.mutate(coverLetter)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-muted/10 to-secondary/5">
      {/* Background Elements */}
      <div className="absolute top-20 left-10 w-72 h-72 bg-primary/10 rounded-full blur-3xl float"></div>
      <div className="absolute bottom-20 right-10 w-96 h-96 bg-accent/10 rounded-full blur-3xl float" style={{animationDelay: '2s'}}></div>

      <main className="max-w-4xl mx-auto p-6 space-y-12 relative">
        {/* Back Button */}
        <div className="animate-in slide-in-from-left duration-500">
          <Link
            href="/jobs"
            className="inline-flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors font-medium group"
          >
            <ArrowLeft className="w-4 h-4 group-hover:-translate-x-1 transition-transform" />
            Back to Jobs
          </Link>
        </div>

        {/* Hero Header */}
        <section className="glass border-0 rounded-3xl p-8 lg:p-12 relative overflow-hidden animate-in fade-in slide-in-from-bottom duration-700">
          {job.isFeatured && (
            <div className="absolute top-4 right-4">
              <div className="px-4 py-2 bg-gradient-to-r from-accent to-orange-500 rounded-full text-white font-bold text-sm animate-pulse flex items-center gap-2">
                <Flame className="h-4 w-4" />
                Featured
              </div>
            </div>
          )}

          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-8">
            <div className="flex items-start gap-6">
              {job.companyLogo && (
                <div className="w-20 h-20 rounded-3xl overflow-hidden bg-muted/50 flex-shrink-0 ring-4 ring-primary/10">
                  <img
                    src={job.companyLogo}
                    alt={`${job.companyName} logo`}
                    className="w-full h-full object-cover"
                  />
                </div>
              )}
              <div className="flex-1">
                <h1 className="text-3xl lg:text-4xl font-black text-foreground mb-3 leading-tight">
                  {job.title}
                </h1>
                <p className="text-xl text-muted-foreground font-semibold mb-4">{job.companyName}</p>

                {/* Quick Stats */}
                <div className="flex flex-wrap gap-3">
                  <div className="flex items-center gap-2 px-3 py-1.5 bg-primary/10 rounded-full">
                    <MapPin className="w-4 h-4 text-primary" />
                    <span className="text-sm font-semibold text-primary">{job.location}</span>
                  </div>
                  <div className="flex items-center gap-2 px-3 py-1.5 bg-accent/10 rounded-full">
                    <span className="text-sm font-semibold text-accent">{getWorkTypeLabel(job.workLocationType || "")}</span>
                  </div>
                  <div className="flex items-center gap-2 px-3 py-1.5 bg-secondary/50 rounded-full">
                    <Clock className="w-4 h-4" />
                    <span className="text-sm font-semibold">{getJobTypeLabel(job.jobType)}</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex flex-col gap-4 lg:items-end">
              <Button size="sm" variant="outline" asChild className="rounded-2xl font-semibold">
                <Link href={`/jobs?search=${encodeURIComponent(job.companyName)}`}>
                  More from {job.companyName} →
                </Link>
              </Button>

              {(job.salaryMin || job.salaryMax || job.salaryNegotiable) && (
                <div className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-2xl border border-green-500/20">
                  <DollarSign className="h-5 w-5 text-green-600" />
                  <span className="font-bold text-green-700 text-lg">{formatSalary(job.salaryMin, job.salaryMax, job.salaryNegotiable)}</span>
                </div>
              )}
            </div>
          </div>
        </section>

        {/* Status & Meta Info */}
        <div className="flex flex-wrap items-center justify-between gap-4 animate-in fade-in slide-in-from-bottom duration-500">
          <div className="flex flex-wrap gap-3">
            <Badge variant="outline" className="rounded-full font-semibold px-4 py-2">
              📂 {job.category}
            </Badge>
            <Badge variant="outline" className="rounded-full font-semibold px-4 py-2">
              🎓 {getExperienceLabel(job.experienceLevel)}
            </Badge>
            <Badge
              variant={job.isActive ? "default" : "destructive"}
              className="rounded-full font-bold px-4 py-2"
            >
              {job.isActive ? "🟢 Open" : "🔴 Closed"}
            </Badge>
            {hasApplied && (
              <Badge className="bg-green-100 text-green-800 border-green-200 rounded-full font-bold px-4 py-2">
                ✅ Applied
              </Badge>
            )}
          </div>

          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Calendar className="w-4 h-4" />
            <span>Posted {formatDate(job.createdAt)}</span>
          </div>
        </div>

        {/* Description & Details */}
        <section className="space-y-8 animate-in fade-in slide-in-from-bottom duration-700">
          <Card className="glass border-0 rounded-lg overflow-hidden">
            <CardHeader className="bg-gradient-to-r from-primary/5 to-accent/5 border-b border-border/50">
              <CardTitle className="text-2xl font-bold flex items-center gap-3">
                <FileText className="h-6 w-6" />
                Job Description
              </CardTitle>
            </CardHeader>
            <CardContent className="p-8">
              <div
                className="prose prose-lg max-w-none prose-headings:font-bold prose-headings:text-foreground prose-p:text-muted-foreground prose-p:leading-relaxed prose-strong:text-foreground prose-ul:text-muted-foreground prose-li:text-muted-foreground"
                dangerouslySetInnerHTML={{ __html: job.description }}
              />
            </CardContent>
          </Card>

          {job.requirements && job.requirements.length > 0 && (
            <Card className="glass border-0 rounded-lg overflow-hidden">
              <CardHeader className="bg-gradient-to-r from-destructive/5 to-orange-500/5 border-b border-border/50">
                <CardTitle className="text-2xl font-bold flex items-center gap-3">
                  ✅ Requirements
                </CardTitle>
              </CardHeader>
              <CardContent className="p-8">
                <ul className="space-y-4">
                  {job.requirements.map((req, idx) => (
                    <li key={idx} className="flex items-start gap-3">
                      <div className="w-6 h-6 rounded-full bg-destructive/10 flex items-center justify-center flex-shrink-0 mt-0.5">
                        <span className="text-destructive font-bold text-sm">•</span>
                      </div>
                      <span className="text-muted-foreground leading-relaxed">{req}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          )}

          {job.responsibilities && job.responsibilities.length > 0 && (
            <Card className="glass border-0 rounded-lg overflow-hidden">
              <CardHeader className="bg-gradient-to-r from-accent/5 to-orange-500/5 border-b border-border/50">
                <CardTitle className="text-2xl font-bold flex items-center gap-3">
                  <Target className="h-6 w-6" />
                  Responsibilities
                </CardTitle>
              </CardHeader>
              <CardContent className="p-8">
                <ul className="space-y-4">
                  {job.responsibilities.map((res, idx) => (
                    <li key={idx} className="flex items-start gap-3">
                      <div className="w-6 h-6 rounded-full bg-accent/10 flex items-center justify-center flex-shrink-0 mt-0.5">
                        <span className="text-accent font-bold text-sm">•</span>
                      </div>
                      <span className="text-muted-foreground leading-relaxed">{res}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          )}
        </section>

        {/* Apply Section */}
        {job.isActive && !hasApplied && (
          <div className="glass border-0 rounded-lg p-8 text-center animate-in fade-in slide-in-from-bottom duration-700">
            <h3 className="text-2xl font-bold text-foreground mb-4 flex items-center justify-center gap-2">
              Ready to Apply? <Rocket className="h-6 w-6" />
            </h3>
            <p className="text-muted-foreground mb-6 max-w-md mx-auto">
              Join {job.companyName} and advance your career. Take the next step in your professional journey!
            </p>
            <Button
              className="w-full max-w-md mx-auto rounded-lg font-semibold h-14 text-lg gradient-primary hover:shadow-lg transition-all duration-200"
              onClick={handleApplyClick}
            >
              Apply Now →
            </Button>
          </div>
        )}

        {/* Apply Dialog */}
        <Dialog open={isApplyDialogOpen} onOpenChange={setIsApplyDialogOpen}>
          <DialogContent className="glass border-0 rounded-lg max-w-lg">
            <DialogHeader className="text-center pb-6">
              <DialogTitle className="text-2xl font-bold flex items-center justify-center gap-2">
                Apply for {job.title} <Target className="h-6 w-6" />
              </DialogTitle>
              <DialogDescription className="text-muted-foreground">
                Submit your application to join the team at {job.companyName}
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-6">
              <div>
                <Label htmlFor="cover" className="text-sm font-bold text-foreground mb-3 block">
                  Cover Letter (Optional)
                </Label>
                <Textarea
                  id="cover"
                  value={coverLetter}
                  onChange={(e) => setCoverLetter(e.target.value)}
                  rows={4}
                  placeholder="Explain why you're interested in this position and how your skills align..."
                  className="rounded-lg border-2 font-medium resize-none"
                />
                <p className="text-xs text-muted-foreground mt-2 flex items-center gap-2">
                  <Lightbulb className="h-3 w-3" />
                  Tip: Mention specific skills or experiences that match the job requirements
                </p>
              </div>
              <div className="flex gap-3">
                <Button
                  onClick={handleApply}
                  disabled={applyMutation.isPending}
                  className="flex-1 rounded-lg font-semibold h-12 gradient-primary"
                >
                  {applyMutation.isPending ? (
                    <>
                      <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                      Submitting...
                    </>
                  ) : (
                    "                    <>
                      Submit Application <Rocket className="ml-2 h-4 w-4" />
                    </>"
                  )}
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setIsApplyDialogOpen(false)}
                  disabled={applyMutation.isPending}
                  className="rounded-lg font-semibold px-6"
                >
                  Cancel
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>

        {/* Sticky Mobile Apply Button */}
        {job.isActive && !hasApplied && (
          <div className="fixed bottom-0 left-0 right-0 p-4 glass border-t md:hidden z-50">
            <Button
              className="w-full rounded-lg font-semibold h-14 gradient-primary"
              onClick={handleApplyClick}
            >
              Apply Now <Rocket className="ml-2 h-4 w-4" />
            </Button>
          </div>
        )}

        {/* Share Section */}
        <div className="pt-8 animate-in fade-in slide-in-from-bottom duration-500">
          <Button
            variant="outline"
            className="w-full rounded-lg font-semibold h-12 hover:shadow-lg transition-all duration-200"
            onClick={() => {
              navigator.clipboard.writeText(window.location.href)
              toast.success("Job link copied to clipboard! 📋")
            }}
          >
            <ExternalLink className="mr-2 h-5 w-5" />
            Share This Job
          </Button>
        </div>
      </main>

      {/* Profile Incomplete Alert */}
      <AlertDialog open={isProfileIncompleteAlertOpen} onOpenChange={setIsProfileIncompleteAlertOpen}>
        <AlertDialogContent className="glass border-0 rounded-lg">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-xl font-bold text-foreground">
              Complete Your Profile to Apply
            </AlertDialogTitle>
            <AlertDialogDescription className="text-muted-foreground">
              You need to complete your profile and upload a resume before applying to this job.
              This helps employers learn more about you and increases your chances of getting hired.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel className="rounded-lg">Cancel</AlertDialogCancel>
            <AlertDialogAction
              className="rounded-lg gradient-primary"
              onClick={() => {
                window.location.href = "/dashboard/profile?welcome=true"
              }}
            >
              Complete Profile
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Update Profile Before Apply Alert */}
      <AlertDialog open={isUpdateProfileAlertOpen} onOpenChange={setIsUpdateProfileAlertOpen}>
        <AlertDialogContent className="glass border-0 rounded-lg">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-xl font-bold text-foreground">
              Ready to Apply?
            </AlertDialogTitle>
            <AlertDialogDescription className="text-muted-foreground">
              Would you like to review and update your profile before applying to this position?
              This ensures your information is current and relevant.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter className="flex-col sm:flex-row gap-2">
            <AlertDialogAction
              className="rounded-lg gradient-primary order-2 sm:order-1"
              onClick={() => {
                setIsUpdateProfileAlertOpen(false)
                setIsApplyDialogOpen(true)
              }}
            >
              Apply Now
            </AlertDialogAction>
            <AlertDialogCancel
              className="rounded-lg order-1 sm:order-2"
              onClick={() => {
                window.location.href = "/dashboard/profile"
              }}
            >
              Update Profile First
            </AlertDialogCancel>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
