"use client"

import React, { useState } from "react"
import Link from "next/link"
import { useAuth } from "@/hooks/use-auth"
import { useMutation, useQueryClient } from "@tanstack/react-query"
import {
  <PERSON><PERSON>,
} from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import {
  MapPin,
  Clock,
  DollarSign,
  Calendar,
  ArrowLeft,
  ExternalLink,
  Loader2,
  Flame,
  FileText,
  Target,
  Rocket,
  Lightbulb
} from "lucide-react"
import {
  Job,
  api,
  formatSalary,
  getJobTypeLabel,
  getWorkLocationTypeLabel,
  getExperienceLevelLabel,
  formatDate,
} from "@/lib/api"
import { toast } from "sonner"

interface JobDetailsClientProps {
  job: Job
  hasApplied: boolean
}

export function JobDetailsClient({ job, hasApplied }: JobDetailsClientProps) {
  const { user, isAuthenticated } = useAuth()
  const [coverLetter, setCoverLetter] = useState("")
  const [isApplyDialogOpen, setIsApplyDialogOpen] = useState(false)
  const [isProfileIncompleteAlertOpen, setIsProfileIncompleteAlertOpen] = useState(false)
  const [isUpdateProfileAlertOpen, setIsUpdateProfileAlertOpen] = useState(false)
  const queryClient = useQueryClient()

  const applyMutation = useMutation({
    mutationFn: (cover: string) => api.applyToJob(job.id, cover),
    onSuccess: (data) => {
      toast.success(data.message || "Application submitted successfully!")
      setIsApplyDialogOpen(false)
      setCoverLetter("")
      queryClient.invalidateQueries({ queryKey: ["my-applications"] })
    },
    onError: (error) => {
      toast.error(
        error instanceof Error ? error.message : "Failed to submit application"
      )
    },
  })

  const checkProfileCompletion = () => {
    if (!isAuthenticated) {
      toast.error("Please sign in to apply for jobs")
      return false
    }

    if (user?.profileCompleted === true) {
      return true
    }

    setIsProfileIncompleteAlertOpen(true)
    return false
  }

  const handleApplyClick = () => {
    if (checkProfileCompletion()) {
      setIsApplyDialogOpen(true)
    }
  }

  const handleApply = () => {
    applyMutation.mutate(coverLetter)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-muted/10 to-secondary/5">
      <main className="max-w-4xl mx-auto p-6 space-y-12 relative">
        <div>
          <Link
            href="/jobs"
            className="inline-flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors font-medium group"
          >
            <ArrowLeft className="w-4 h-4 group-hover:-translate-x-1 transition-transform" />
            Back to Jobs
          </Link>
        </div>

        <section className="glass border-0 rounded-3xl p-8 lg:p-12 relative overflow-hidden">
          {job.isFeatured && (
            <div className="absolute top-4 right-4">
              <div className="px-4 py-2 bg-gradient-to-r from-accent to-orange-500 rounded-full text-white font-bold text-sm animate-pulse flex items-center gap-2">
                <Flame className="h-4 w-4" />
                Featured
              </div>
            </div>
          )}

          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-8">
            <div className="flex-1">
              <div className="flex items-center gap-4 mb-6">
                {job.companyLogo && (
                  <img
                    src={job.companyLogo}
                    alt={`${job.companyName} logo`}
                    className="w-16 h-16 rounded-lg object-cover border border-border/50"
                  />
                )}
                <div>
                  <h1 className="text-4xl font-bold text-foreground mb-2">{job.title}</h1>
                  <div className="flex items-center gap-2 text-lg text-muted-foreground">
                    <span className="font-semibold">{job.companyName}</span>
                    {job.companyWebsite && (
                      <Link
                        href={job.companyWebsite}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-primary hover:underline inline-flex items-center gap-1"
                      >
                        <ExternalLink className="w-4 h-4" />
                      </Link>
                    )}
                  </div>
                </div>
              </div>

              <div className="flex flex-wrap gap-3 mb-6">
                <Badge variant="secondary" className="text-sm font-medium">
                  <MapPin className="w-4 h-4 mr-2" />
                  {job.location}
                </Badge>
                <Badge variant="secondary" className="text-sm font-medium">
                  <Clock className="w-4 h-4 mr-2" />
                  {getJobTypeLabel(job.jobType)}
                </Badge>
                <Badge variant="secondary" className="text-sm font-medium">
                  {getWorkLocationTypeLabel(job.workLocationType)}
                </Badge>
                <Badge variant="secondary" className="text-sm font-medium">
                  {getExperienceLevelLabel(job.experienceLevel)}
                </Badge>
                <Badge variant="outline" className="text-sm font-medium">
                  {job.category}
                </Badge>
              </div>

              {(job.salaryMin || job.salaryMax || job.salaryNegotiable) && (
                <div className="flex items-center gap-2 mb-4">
                  <DollarSign className="w-5 h-5 text-green-600" />
                  <span className="font-bold text-green-700 text-lg">{formatSalary(job.salaryMin, job.salaryMax, job.salaryNegotiable)}</span>
                </div>
              )}
            </div>
          </div>
        </section>

        <div className="flex flex-wrap items-center justify-between gap-4">
          <div className="flex items-center gap-4">
            <Badge
              variant={job.isActive ? "default" : "secondary"}
              className="text-sm font-semibold"
            >
              {job.isActive ? "Active" : "Closed"}
            </Badge>
            {job.applicationDeadline && (
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Calendar className="w-4 h-4" />
                <span>Apply by {formatDate(job.applicationDeadline)}</span>
              </div>
            )}
          </div>

          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Calendar className="w-4 h-4" />
            <span>Posted {formatDate(job.createdAt)}</span>
          </div>
        </div>

        <section className="space-y-8">
          <Card className="glass border-0 rounded-lg overflow-hidden">
            <CardHeader className="bg-gradient-to-r from-primary/5 to-accent/5 border-b border-border/50">
              <CardTitle className="text-2xl font-bold flex items-center gap-3">
                <FileText className="h-6 w-6" />
                Job Description
              </CardTitle>
            </CardHeader>
            <CardContent className="p-8">
              <div
                className="prose prose-lg max-w-none prose-headings:font-bold prose-headings:text-foreground prose-p:text-muted-foreground prose-p:leading-relaxed prose-strong:text-foreground prose-ul:text-muted-foreground prose-li:text-muted-foreground"
                dangerouslySetInnerHTML={{ __html: job.description }}
              />
            </CardContent>
          </Card>

          {job.responsibilities && job.responsibilities.length > 0 && (
            <Card className="glass border-0 rounded-lg overflow-hidden">
              <CardHeader className="bg-gradient-to-r from-accent/5 to-orange-500/5 border-b border-border/50">
                <CardTitle className="text-2xl font-bold flex items-center gap-3">
                  <Target className="h-6 w-6" />
                  Responsibilities
                </CardTitle>
              </CardHeader>
              <CardContent className="p-8">
                <ul className="space-y-4">
                  {job.responsibilities.map((res, idx) => (
                    <li key={idx} className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-accent rounded-full mt-3 flex-shrink-0"></div>
                      <span className="text-muted-foreground leading-relaxed">{res}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          )}
        </section>

        {job.isActive && !hasApplied && (
          <div className="glass border-0 rounded-lg p-8 text-center">
            <h3 className="text-2xl font-bold text-foreground mb-4 flex items-center justify-center gap-2">
              Ready to Apply? <Rocket className="h-6 w-6" />
            </h3>
            <p className="text-muted-foreground mb-6 max-w-md mx-auto">
              Join {job.companyName} and advance your career. Take the next step in your professional journey!
            </p>
            <Button
              className="w-full max-w-md mx-auto rounded-lg font-semibold h-14 text-lg gradient-primary hover:shadow-lg transition-all duration-200"
              onClick={handleApplyClick}
            >
              Apply Now →
            </Button>
          </div>
        )}

        <Dialog open={isApplyDialogOpen} onOpenChange={setIsApplyDialogOpen}>
          <DialogContent className="glass border-0 rounded-lg max-w-lg">
            <DialogHeader className="text-center pb-6">
              <DialogTitle className="text-2xl font-bold flex items-center justify-center gap-2">
                Apply for {job.title} <Target className="h-6 w-6" />
              </DialogTitle>
              <DialogDescription className="text-muted-foreground">
                Submit your application to join the team at {job.companyName}
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-6">
              <div>
                <Label htmlFor="coverLetter" className="text-sm font-bold text-foreground mb-3 block">
                  Cover Letter
                </Label>
                <Textarea
                  id="coverLetter"
                  value={coverLetter}
                  onChange={(e) => setCoverLetter(e.target.value)}
                  rows={4}
                  placeholder="Explain why you're interested in this position and how your skills align..."
                  className="rounded-lg border-2 font-medium resize-none"
                />
                <p className="text-xs text-muted-foreground mt-2 flex items-center gap-2">
                  <Lightbulb className="h-3 w-3" />
                  Tip: Mention specific skills or experiences that match the job requirements
                </p>
              </div>
              <div className="flex gap-3">
                <Button
                  onClick={handleApply}
                  disabled={applyMutation.isPending || !coverLetter.trim()}
                  className="flex-1 rounded-lg font-semibold h-12 gradient-primary"
                >
                  {applyMutation.isPending ? (
                    <>
                      <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                      Submitting...
                    </>
                  ) : (
                    <>
                      Submit Application <Rocket className="ml-2 h-4 w-4" />
                    </>
                  )}
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setIsApplyDialogOpen(false)}
                  className="rounded-lg font-semibold h-12"
                >
                  Cancel
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>

        {job.isActive && !hasApplied && (
          <div className="fixed bottom-0 left-0 right-0 p-4 glass border-t md:hidden z-50">
            <Button
              className="w-full rounded-lg font-semibold h-14 gradient-primary"
              onClick={handleApplyClick}
            >
              Apply Now <Rocket className="ml-2 h-4 w-4" />
            </Button>
          </div>
        )}

        <div className="pt-8">
          <Button
            variant="outline"
            className="w-full rounded-lg font-semibold h-12"
            onClick={() => {
              navigator.share?.({
                title: job.title,
                text: `Check out this job at ${job.companyName}`,
                url: window.location.href,
              })
            }}
          >
            <ExternalLink className="mr-2 h-5 w-5" />
            Share This Job
          </Button>
        </div>
      </main>

      <AlertDialog open={isProfileIncompleteAlertOpen} onOpenChange={setIsProfileIncompleteAlertOpen}>
        <AlertDialogContent className="glass border-0 rounded-lg">
          <AlertDialogHeader>
            <AlertDialogTitle>Complete Your Profile</AlertDialogTitle>
            <AlertDialogDescription>
              You need to complete your profile before applying for jobs. This helps employers learn more about you.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogAction asChild>
              <Link href="/dashboard/profile">
                Complete Profile
              </Link>
            </AlertDialogAction>
            <AlertDialogCancel>
              Maybe Later
            </AlertDialogCancel>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <AlertDialog open={isUpdateProfileAlertOpen} onOpenChange={setIsUpdateProfileAlertOpen}>
        <AlertDialogContent className="glass border-0 rounded-lg">
          <AlertDialogHeader>
            <AlertDialogTitle>Update Your Profile?</AlertDialogTitle>
            <AlertDialogDescription>
              Would you like to review and update your profile before applying? This can help improve your chances.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogAction
              onClick={() => {
                setIsUpdateProfileAlertOpen(false)
                setIsApplyDialogOpen(true)
              }}
            >
              Apply Now
            </AlertDialogAction>
            <AlertDialogCancel asChild>
              <Link href="/dashboard/profile">
                Update Profile First
              </Link>
            </AlertDialogCancel>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
