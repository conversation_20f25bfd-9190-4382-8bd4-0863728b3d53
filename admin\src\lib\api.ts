import axios from 'axios'

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api'

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('admin_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor to handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('admin_token')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

// Auth API
export const authAPI = {
  login: (credentials: { email: string; password: string }) =>
    api.post('/auth/admin/login', credentials),

  register: (data: { email: string; password: string; name: string; secretKey: string }) =>
    api.post('/auth/admin/register', data),

  getProfile: () =>
    api.get('/auth/admin/profile'),
}

// Dashboard API
export const dashboardAPI = {
  getStats: () =>
    api.get('/admin/dashboard/stats'),

  getRecentActivity: () =>
    api.get('/admin/dashboard/recent-activity'),
}

// Jobs API
export const jobsAPI = {
  getJobs: (params?: any) =>
    api.get('/admin/jobs', { params }),

  getJob: (id: string) =>
    api.get(`/admin/jobs/${id}`),

  createJob: (data: any) =>
    api.post('/admin/jobs', data),

  updateJob: (id: string, data: any) =>
    api.put(`/admin/jobs/${id}`, data),

  deleteJob: (id: string) =>
    api.delete(`/admin/jobs/${id}`),

  toggleJobStatus: (id: string) =>
    api.patch(`/admin/jobs/${id}/toggle-status`),
}

// Users API
export const usersAPI = {
  getUsers: (params?: any) =>
    api.get('/admin/users', { params }),

  getUser: (id: string) =>
    api.get(`/admin/users/${id}`),

  updateUser: (id: string, data: any) =>
    api.put(`/admin/users/${id}`, data),

  deleteUser: (id: string) =>
    api.delete(`/admin/users/${id}`),
}

// Applications API
export const applicationsAPI = {
  getApplications: (params?: any) =>
    api.get('/admin/applications', { params }),

  getApplication: (id: string) =>
    api.get(`/admin/applications/${id}`),

  updateApplicationStatus: (id: string, status: string) =>
    api.patch(`/admin/applications/${id}/status`, { status }),

  deleteApplication: (id: string) =>
    api.delete(`/admin/applications/${id}`),
}

// Blogs API
export const blogsAPI = {
  getBlogs: (params?: any) =>
    api.get('/admin/blogs', { params }),

  getBlog: (id: string) =>
    api.get(`/admin/blogs/${id}`),

  createBlog: (data: any) =>
    api.post('/admin/blogs', data),

  updateBlog: (id: string, data: any) =>
    api.put(`/admin/blogs/${id}`, data),

  deleteBlog: (id: string) =>
    api.delete(`/admin/blogs/${id}`),

  toggleBlogStatus: (id: string) =>
    api.patch(`/admin/blogs/${id}/toggle-status`),
}

// Analytics API
export const analyticsAPI = {
  getJobStats: (period?: string) =>
    api.get('/admin/analytics/jobs', { params: { period } }),

  getUserStats: (period?: string) =>
    api.get('/admin/analytics/users', { params: { period } }),

  getApplicationStats: (period?: string) =>
    api.get('/admin/analytics/applications', { params: { period } }),
}

export default api
