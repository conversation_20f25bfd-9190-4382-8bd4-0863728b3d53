"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.authController = void 0;
const db_1 = require("../lib/db");
const utils_1 = require("../lib/utils");
const jwt_1 = require("../lib/jwt");
const auth_1 = require("../schemas/auth");
const errorHandler_1 = require("../middleware/errorHandler");
const email_1 = require("../lib/email");
exports.authController = {
    register: async (req, res, next) => {
        try {
            const validatedData = auth_1.registerSchema.parse(req.body);
            const existingUser = await db_1.db.user.findUnique({
                where: { email: validatedData.email }
            });
            if (existingUser) {
                throw (0, errorHandler_1.createError)('User already exists with this email', 409);
            }
            const hashedPassword = await (0, utils_1.hashPassword)(validatedData.password);
            const verificationToken = (0, email_1.generateToken)();
            const verificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000);
            const nameParts = validatedData.fullName.trim().split(' ');
            const firstName = nameParts[0];
            const lastName = nameParts.length > 1 ? nameParts.slice(1).join(' ') : '';
            const user = await db_1.db.user.create({
                data: {
                    email: validatedData.email,
                    password: hashedPassword,
                    firstName: firstName,
                    lastName: lastName,
                    phone: validatedData.phone,
                    experienceLevel: validatedData.experienceLevel,
                    emailVerificationToken: verificationToken,
                    emailVerificationExpires: verificationExpires,
                },
                select: {
                    id: true,
                    email: true,
                    firstName: true,
                    lastName: true,
                    phone: true,
                    experienceLevel: true,
                    role: true,
                    profileCompleted: true,
                    emailVerified: true,
                    createdAt: true,
                }
            });
            const emailSent = await (0, email_1.sendVerificationEmail)(user.email, verificationToken);
            res.status(201).json({
                message: 'User registered successfully. Please check your email to verify your account.',
                user,
                emailSent,
                requiresVerification: true,
            });
        }
        catch (error) {
            next(error);
        }
    },
    login: async (req, res, next) => {
        try {
            const validatedData = auth_1.loginSchema.parse(req.body);
            const user = await db_1.db.user.findUnique({
                where: { email: validatedData.email }
            });
            if (!user) {
                throw (0, errorHandler_1.createError)('Invalid email or password', 401);
            }
            const isPasswordValid = await (0, utils_1.comparePassword)(validatedData.password, user.password);
            if (!isPasswordValid) {
                throw (0, errorHandler_1.createError)('Invalid email or password', 401);
            }
            if (!user.emailVerified) {
                throw (0, errorHandler_1.createError)('Please verify your email address before logging in. Check your inbox for the verification link.', 403);
            }
            const token = (0, jwt_1.generateToken)({
                userId: user.id,
                email: user.email,
                role: user.role,
            });
            const { password, ...userWithoutPassword } = user;
            res.json({
                message: 'Login successful',
                user: userWithoutPassword,
                token,
            });
        }
        catch (error) {
            next(error);
        }
    },
    getMe: async (req, res, next) => {
        try {
            const user = await db_1.db.user.findUnique({
                where: { id: req.user.id },
                select: {
                    id: true,
                    email: true,
                    phone: true,
                    experienceLevel: true,
                    role: true,
                    emailVerified: true,
                    firstName: true,
                    lastName: true,
                    profilePicture: true,
                    resume: true,
                    bio: true,
                    skills: true,
                    experience: true,
                    education: true,
                    location: true,
                    website: true,
                    linkedin: true,
                    github: true,
                    profileCompleted: true,
                    createdAt: true,
                    updatedAt: true,
                }
            });
            if (!user) {
                throw (0, errorHandler_1.createError)('User not found', 404);
            }
            res.json({ user });
        }
        catch (error) {
            next(error);
        }
    },
    logout: (req, res) => {
        res.json({ message: 'Logout successful' });
    },
    adminRegister: async (req, res, next) => {
        try {
            const { email, password, name, secretKey } = req.body;
            if (!email || !password || !name || !secretKey) {
                throw (0, errorHandler_1.createError)('All fields are required', 400);
            }
            const ADMIN_SECRET_KEY = process.env.ADMIN_SECRET_KEY || 'admin-secret-2024';
            if (secretKey !== ADMIN_SECRET_KEY) {
                throw (0, errorHandler_1.createError)('Invalid secret key', 401);
            }
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                throw (0, errorHandler_1.createError)('Invalid email format', 400);
            }
            if (password.length < 8) {
                throw (0, errorHandler_1.createError)('Password must be at least 8 characters long', 400);
            }
            const existingAdmin = await db_1.db.user.findUnique({
                where: { email }
            });
            if (existingAdmin) {
                throw (0, errorHandler_1.createError)('Admin already exists with this email', 409);
            }
            const hashedPassword = await (0, utils_1.hashPassword)(password);
            const nameParts = name.trim().split(' ');
            const firstName = nameParts[0];
            const lastName = nameParts.length > 1 ? nameParts.slice(1).join(' ') : '';
            const admin = await db_1.db.user.create({
                data: {
                    email,
                    password: hashedPassword,
                    firstName: firstName,
                    lastName: lastName,
                    role: 'ADMIN',
                    profileCompleted: true,
                },
                select: {
                    id: true,
                    email: true,
                    firstName: true,
                    lastName: true,
                    role: true,
                    createdAt: true,
                }
            });
            const token = (0, jwt_1.generateToken)({
                userId: admin.id,
                email: admin.email,
                role: admin.role,
            });
            res.status(201).json({
                message: 'Admin registered successfully',
                admin,
                token,
            });
        }
        catch (error) {
            next(error);
        }
    },
    adminLogin: async (req, res, next) => {
        try {
            const validatedData = auth_1.loginSchema.parse(req.body);
            const admin = await db_1.db.user.findUnique({
                where: {
                    email: validatedData.email,
                }
            });
            if (!admin || admin.role !== 'ADMIN') {
                throw (0, errorHandler_1.createError)('Invalid email or password', 401);
            }
            const isPasswordValid = await (0, utils_1.comparePassword)(validatedData.password, admin.password);
            if (!isPasswordValid) {
                throw (0, errorHandler_1.createError)('Invalid email or password', 401);
            }
            const token = (0, jwt_1.generateToken)({
                userId: admin.id,
                email: admin.email,
                role: admin.role,
            });
            const { password, ...adminWithoutPassword } = admin;
            res.json({
                message: 'Admin login successful',
                admin: adminWithoutPassword,
                token,
            });
        }
        catch (error) {
            next(error);
        }
    },
    getAdminProfile: async (req, res, next) => {
        try {
            if (req.user.role !== 'ADMIN') {
                throw (0, errorHandler_1.createError)('Admin access required', 403);
            }
            const admin = await db_1.db.user.findUnique({
                where: { id: req.user.id },
                select: {
                    id: true,
                    email: true,
                    firstName: true,
                    lastName: true,
                    role: true,
                    createdAt: true,
                    updatedAt: true,
                }
            });
            if (!admin) {
                throw (0, errorHandler_1.createError)('Admin not found', 404);
            }
            res.json({ admin });
        }
        catch (error) {
            next(error);
        }
    },
    sendVerification: async (req, res, next) => {
        try {
            const validatedData = auth_1.sendVerificationSchema.parse(req.body);
            const user = await db_1.db.user.findUnique({
                where: { email: validatedData.email }
            });
            if (!user) {
                throw (0, errorHandler_1.createError)('User not found', 404);
            }
            if (user.emailVerified) {
                throw (0, errorHandler_1.createError)('Email is already verified', 400);
            }
            const verificationToken = (0, email_1.generateToken)();
            const verificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000);
            await db_1.db.user.update({
                where: { id: user.id },
                data: {
                    emailVerificationToken: verificationToken,
                    emailVerificationExpires: verificationExpires,
                }
            });
            const emailSent = await (0, email_1.sendVerificationEmail)(user.email, verificationToken);
            res.json({
                message: 'Verification email sent successfully',
                emailSent,
            });
        }
        catch (error) {
            next(error);
        }
    },
    verifyEmail: async (req, res, next) => {
        try {
            const validatedData = auth_1.verifyEmailSchema.parse(req.body);
            const user = await db_1.db.user.findUnique({
                where: { emailVerificationToken: validatedData.token }
            });
            if (!user) {
                throw (0, errorHandler_1.createError)('Invalid or expired verification token', 400);
            }
            if (user.emailVerificationExpires && user.emailVerificationExpires < new Date()) {
                throw (0, errorHandler_1.createError)('Verification token has expired', 400);
            }
            const updatedUser = await db_1.db.user.update({
                where: { id: user.id },
                data: {
                    emailVerified: true,
                    emailVerificationToken: null,
                    emailVerificationExpires: null,
                },
                select: {
                    id: true,
                    email: true,
                    emailVerified: true,
                    role: true,
                }
            });
            const token = (0, jwt_1.generateToken)({
                userId: updatedUser.id,
                email: updatedUser.email,
                role: updatedUser.role,
            });
            res.json({
                message: 'Email verified successfully',
                user: updatedUser,
                token,
            });
        }
        catch (error) {
            next(error);
        }
    },
    forgotPassword: async (req, res, next) => {
        try {
            const validatedData = auth_1.forgotPasswordSchema.parse(req.body);
            const user = await db_1.db.user.findUnique({
                where: { email: validatedData.email }
            });
            if (!user) {
                res.json({
                    message: 'If an account with that email exists, we have sent a password reset link.',
                });
                return;
            }
            const resetToken = (0, email_1.generateToken)();
            const resetExpires = new Date(Date.now() + 60 * 60 * 1000);
            await db_1.db.user.update({
                where: { id: user.id },
                data: {
                    passwordResetToken: resetToken,
                    passwordResetExpires: resetExpires,
                }
            });
            const emailSent = await (0, email_1.sendPasswordResetEmail)(user.email, resetToken);
            res.json({
                message: 'If an account with that email exists, we have sent a password reset link.',
                emailSent,
            });
        }
        catch (error) {
            next(error);
        }
    },
    resetPassword: async (req, res, next) => {
        try {
            const validatedData = auth_1.resetPasswordSchema.parse(req.body);
            const user = await db_1.db.user.findUnique({
                where: { passwordResetToken: validatedData.token }
            });
            if (!user) {
                throw (0, errorHandler_1.createError)('Invalid or expired reset token', 400);
            }
            if (user.passwordResetExpires && user.passwordResetExpires < new Date()) {
                throw (0, errorHandler_1.createError)('Reset token has expired', 400);
            }
            const hashedPassword = await (0, utils_1.hashPassword)(validatedData.password);
            const updatedUser = await db_1.db.user.update({
                where: { id: user.id },
                data: {
                    password: hashedPassword,
                    passwordResetToken: null,
                    passwordResetExpires: null,
                },
                select: {
                    id: true,
                    email: true,
                    role: true,
                }
            });
            const token = (0, jwt_1.generateToken)({
                userId: updatedUser.id,
                email: updatedUser.email,
                role: updatedUser.role,
            });
            res.json({
                message: 'Password reset successfully',
                user: updatedUser,
                token,
            });
        }
        catch (error) {
            next(error);
        }
    }
};
//# sourceMappingURL=authController.js.map