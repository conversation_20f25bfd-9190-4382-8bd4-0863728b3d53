"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const db_1 = require("../lib/db");
const blog_1 = require("../schemas/blog");
const errorHandler_1 = require("../middleware/errorHandler");
const utils_1 = require("../lib/utils");
const router = express_1.default.Router();
router.get('/', async (req, res, next) => {
    try {
        const filters = blog_1.blogFiltersSchema.parse({
            ...req.query,
            isPublished: true,
        });
        const { page, limit, skip } = (0, utils_1.getPaginationParams)(filters);
        const where = {
            isPublished: true,
        };
        if (filters.search) {
            where.OR = [
                { title: { contains: filters.search, mode: 'insensitive' } },
                { excerpt: { contains: filters.search, mode: 'insensitive' } },
                { content: { contains: filters.search, mode: 'insensitive' } },
            ];
        }
        const [blogs, total] = await Promise.all([
            db_1.db.blog.findMany({
                where,
                select: {
                    id: true,
                    title: true,
                    slug: true,
                    excerpt: true,
                    featuredImage: true,
                    publishedAt: true,
                    createdAt: true,
                },
                orderBy: { publishedAt: 'desc' },
                skip,
                take: limit,
            }),
            db_1.db.blog.count({ where })
        ]);
        const result = (0, utils_1.createPaginationResult)(blogs, total, page, limit);
        res.json({
            blogs: result.data,
            pagination: result.pagination
        });
    }
    catch (error) {
        next(error);
    }
});
router.get('/latest', async (req, res, next) => {
    try {
        const limit = Math.min(parseInt(req.query.limit) || 5, 20);
        const blogs = await db_1.db.blog.findMany({
            where: { isPublished: true },
            select: {
                id: true,
                title: true,
                slug: true,
                excerpt: true,
                featuredImage: true,
                publishedAt: true,
            },
            orderBy: { publishedAt: 'desc' },
            take: limit,
        });
        res.json({ blogs });
    }
    catch (error) {
        next(error);
    }
});
router.get('/:slug', async (req, res, next) => {
    try {
        const { slug } = req.params;
        const blog = await db_1.db.blog.findUnique({
            where: {
                slug,
                isPublished: true
            },
            select: {
                id: true,
                title: true,
                slug: true,
                content: true,
                excerpt: true,
                featuredImage: true,
                metaTitle: true,
                metaDescription: true,
                metaKeywords: true,
                structuredData: true,
                publishedAt: true,
                createdAt: true,
                updatedAt: true,
            }
        });
        if (!blog) {
            throw (0, errorHandler_1.createError)('Blog not found', 404);
        }
        const relatedBlogs = await db_1.db.blog.findMany({
            where: {
                isPublished: true,
                id: { not: blog.id },
            },
            select: {
                id: true,
                title: true,
                slug: true,
                excerpt: true,
                featuredImage: true,
                publishedAt: true,
            },
            orderBy: { publishedAt: 'desc' },
            take: 3,
        });
        res.json({ blog, relatedBlogs });
    }
    catch (error) {
        next(error);
    }
});
exports.default = router;
//# sourceMappingURL=blogs.js.map