import { create } from 'zustand'
import { persist } from 'zustand/middleware'

export interface Admin {
  id: string
  email: string
  name: string
  role: string
  createdAt: string
}

interface AuthState {
  admin: Admin | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean
  login: (admin: Admin, token: string) => void
  logout: () => void
  setLoading: (loading: boolean) => void
  updateAdmin: (admin: Partial<Admin>) => void
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      admin: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,

      login: (admin: Admin, token: string) => {
        localStorage.setItem('admin_token', token)
        set({
          admin,
          token,
          isAuthenticated: true,
          isLoading: false,
        })
      },

      logout: () => {
        localStorage.removeItem('admin_token')
        set({
          admin: null,
          token: null,
          isAuthenticated: false,
          isLoading: false,
        })
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading })
      },

      updateAdmin: (adminUpdate: Partial<Admin>) => {
        const currentAdmin = get().admin
        if (currentAdmin) {
          set({
            admin: { ...currentAdmin, ...adminUpdate },
          })
        }
      },
    }),
    {
      name: 'admin-auth-storage',
      partialize: (state) => ({
        admin: state.admin,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
)

// Dashboard Store
interface DashboardState {
  stats: {
    totalJobs: number
    totalUsers: number
    totalApplications: number
    activeJobs: number
  } | null
  recentActivity: any[]
  isLoading: boolean
  setStats: (stats: any) => void
  setRecentActivity: (activity: any[]) => void
  setLoading: (loading: boolean) => void
}

export const useDashboardStore = create<DashboardState>((set) => ({
  stats: null,
  recentActivity: [],
  isLoading: false,

  setStats: (stats) => set({ stats }),
  setRecentActivity: (recentActivity) => set({ recentActivity }),
  setLoading: (isLoading) => set({ isLoading }),
}))

// Jobs Store
interface JobsState {
  jobs: any[]
  currentJob: any | null
  isLoading: boolean
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  } | null
  setJobs: (jobs: any[]) => void
  setCurrentJob: (job: any) => void
  setPagination: (pagination: any) => void
  setLoading: (loading: boolean) => void
  addJob: (job: any) => void
  updateJob: (id: string, job: any) => void
  removeJob: (id: string) => void
}

export const useJobsStore = create<JobsState>((set, get) => ({
  jobs: [],
  currentJob: null,
  isLoading: false,
  pagination: null,

  setJobs: (jobs) => set({ jobs }),
  setCurrentJob: (currentJob) => set({ currentJob }),
  setPagination: (pagination) => set({ pagination }),
  setLoading: (isLoading) => set({ isLoading }),

  addJob: (job) => {
    const jobs = get().jobs
    set({ jobs: [job, ...jobs] })
  },

  updateJob: (id, updatedJob) => {
    const jobs = get().jobs
    const updatedJobs = jobs.map((job) =>
      job.id === id ? { ...job, ...updatedJob } : job
    )
    set({ jobs: updatedJobs })
  },

  removeJob: (id) => {
    const jobs = get().jobs
    const filteredJobs = jobs.filter((job) => job.id !== id)
    set({ jobs: filteredJobs })
  },
}))

// Users Store
interface UsersState {
  users: any[]
  currentUser: any | null
  isLoading: boolean
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  } | null
  setUsers: (users: any[]) => void
  setCurrentUser: (user: any) => void
  setPagination: (pagination: any) => void
  setLoading: (loading: boolean) => void
  updateUser: (id: string, user: any) => void
  removeUser: (id: string) => void
}

export const useUsersStore = create<UsersState>((set, get) => ({
  users: [],
  currentUser: null,
  isLoading: false,
  pagination: null,

  setUsers: (users) => set({ users }),
  setCurrentUser: (currentUser) => set({ currentUser }),
  setPagination: (pagination) => set({ pagination }),
  setLoading: (isLoading) => set({ isLoading }),

  updateUser: (id, updatedUser) => {
    const users = get().users
    const updatedUsers = users.map((user) =>
      user.id === id ? { ...user, ...updatedUser } : user
    )
    set({ users: updatedUsers })
  },

  removeUser: (id) => {
    const users = get().users
    const filteredUsers = users.filter((user) => user.id !== id)
    set({ users: filteredUsers })
  },
}))

// Applications Store
interface ApplicationsState {
  applications: any[]
  currentApplication: any | null
  isLoading: boolean
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  } | null
  setApplications: (applications: any[]) => void
  setCurrentApplication: (application: any) => void
  setPagination: (pagination: any) => void
  setLoading: (loading: boolean) => void
  updateApplication: (id: string, application: any) => void
  removeApplication: (id: string) => void
}

export const useApplicationsStore = create<ApplicationsState>((set, get) => ({
  applications: [],
  currentApplication: null,
  isLoading: false,
  pagination: null,

  setApplications: (applications) => set({ applications }),
  setCurrentApplication: (currentApplication) => set({ currentApplication }),
  setPagination: (pagination) => set({ pagination }),
  setLoading: (isLoading) => set({ isLoading }),

  updateApplication: (id, updatedApplication) => {
    const applications = get().applications
    const updatedApplications = applications.map((app) =>
      app.id === id ? { ...app, ...updatedApplication } : app
    )
    set({ applications: updatedApplications })
  },

  removeApplication: (id) => {
    const applications = get().applications
    const filteredApplications = applications.filter((app) => app.id !== id)
    set({ applications: filteredApplications })
  },
}))

// Blogs Store
interface BlogsState {
  blogs: any[]
  currentBlog: any | null
  isLoading: boolean
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  } | null
  setBlogs: (blogs: any[]) => void
  setCurrentBlog: (blog: any) => void
  setPagination: (pagination: any) => void
  setLoading: (loading: boolean) => void
  addBlog: (blog: any) => void
  updateBlog: (id: string, blog: any) => void
  removeBlog: (id: string) => void
}

export const useBlogsStore = create<BlogsState>((set, get) => ({
  blogs: [],
  currentBlog: null,
  isLoading: false,
  pagination: null,

  setBlogs: (blogs) => set({ blogs }),
  setCurrentBlog: (currentBlog) => set({ currentBlog }),
  setPagination: (pagination) => set({ pagination }),
  setLoading: (isLoading) => set({ isLoading }),

  addBlog: (blog) => {
    const blogs = get().blogs
    set({ blogs: [blog, ...blogs] })
  },

  updateBlog: (id, updatedBlog) => {
    const blogs = get().blogs
    const updatedBlogs = blogs.map((blog) =>
      blog.id === id ? { ...blog, ...updatedBlog } : blog
    )
    set({ blogs: updatedBlogs })
  },

  removeBlog: (id) => {
    const blogs = get().blogs
    const filteredBlogs = blogs.filter((blog) => blog.id !== id)
    set({ blogs: filteredBlogs })
  },
}))
