{"version": 3, "sources": ["../src/dialog.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContext, createContextScope } from '@radix-ui/react-context';\nimport { useId } from '@radix-ui/react-id';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { DismissableLayer } from '@radix-ui/react-dismissable-layer';\nimport { FocusScope } from '@radix-ui/react-focus-scope';\nimport { Portal as PortalPrimitive } from '@radix-ui/react-portal';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useFocusGuards } from '@radix-ui/react-focus-guards';\nimport { RemoveScroll } from 'react-remove-scroll';\nimport { hideOthers } from 'aria-hidden';\nimport { createSlot } from '@radix-ui/react-slot';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Dialog\n * -----------------------------------------------------------------------------------------------*/\n\nconst DIALOG_NAME = 'Dialog';\n\ntype ScopedProps<P> = P & { __scopeDialog?: Scope };\nconst [createDialogContext, createDialogScope] = createContextScope(DIALOG_NAME);\n\ntype DialogContextValue = {\n  triggerRef: React.RefObject<HTMLButtonElement | null>;\n  contentRef: React.RefObject<DialogContentElement | null>;\n  contentId: string;\n  titleId: string;\n  descriptionId: string;\n  open: boolean;\n  onOpenChange(open: boolean): void;\n  onOpenToggle(): void;\n  modal: boolean;\n};\n\nconst [DialogProvider, useDialogContext] = createDialogContext<DialogContextValue>(DIALOG_NAME);\n\ninterface DialogProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?(open: boolean): void;\n  modal?: boolean;\n}\n\nconst Dialog: React.FC<DialogProps> = (props: ScopedProps<DialogProps>) => {\n  const {\n    __scopeDialog,\n    children,\n    open: openProp,\n    defaultOpen,\n    onOpenChange,\n    modal = true,\n  } = props;\n  const triggerRef = React.useRef<HTMLButtonElement>(null);\n  const contentRef = React.useRef<DialogContentElement>(null);\n  const [open, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen ?? false,\n    onChange: onOpenChange,\n    caller: DIALOG_NAME,\n  });\n\n  return (\n    <DialogProvider\n      scope={__scopeDialog}\n      triggerRef={triggerRef}\n      contentRef={contentRef}\n      contentId={useId()}\n      titleId={useId()}\n      descriptionId={useId()}\n      open={open}\n      onOpenChange={setOpen}\n      onOpenToggle={React.useCallback(() => setOpen((prevOpen) => !prevOpen), [setOpen])}\n      modal={modal}\n    >\n      {children}\n    </DialogProvider>\n  );\n};\n\nDialog.displayName = DIALOG_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'DialogTrigger';\n\ntype DialogTriggerElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface DialogTriggerProps extends PrimitiveButtonProps {}\n\nconst DialogTrigger = React.forwardRef<DialogTriggerElement, DialogTriggerProps>(\n  (props: ScopedProps<DialogTriggerProps>, forwardedRef) => {\n    const { __scopeDialog, ...triggerProps } = props;\n    const context = useDialogContext(TRIGGER_NAME, __scopeDialog);\n    const composedTriggerRef = useComposedRefs(forwardedRef, context.triggerRef);\n    return (\n      <Primitive.button\n        type=\"button\"\n        aria-haspopup=\"dialog\"\n        aria-expanded={context.open}\n        aria-controls={context.contentId}\n        data-state={getState(context.open)}\n        {...triggerProps}\n        ref={composedTriggerRef}\n        onClick={composeEventHandlers(props.onClick, context.onOpenToggle)}\n      />\n    );\n  }\n);\n\nDialogTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'DialogPortal';\n\ntype PortalContextValue = { forceMount?: true };\nconst [PortalProvider, usePortalContext] = createDialogContext<PortalContextValue>(PORTAL_NAME, {\n  forceMount: undefined,\n});\n\ntype PortalProps = React.ComponentPropsWithoutRef<typeof PortalPrimitive>;\ninterface DialogPortalProps {\n  children?: React.ReactNode;\n  /**\n   * Specify a container element to portal the content into.\n   */\n  container?: PortalProps['container'];\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst DialogPortal: React.FC<DialogPortalProps> = (props: ScopedProps<DialogPortalProps>) => {\n  const { __scopeDialog, forceMount, children, container } = props;\n  const context = useDialogContext(PORTAL_NAME, __scopeDialog);\n  return (\n    <PortalProvider scope={__scopeDialog} forceMount={forceMount}>\n      {React.Children.map(children, (child) => (\n        <Presence present={forceMount || context.open}>\n          <PortalPrimitive asChild container={container}>\n            {child}\n          </PortalPrimitive>\n        </Presence>\n      ))}\n    </PortalProvider>\n  );\n};\n\nDialogPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogOverlay\n * -----------------------------------------------------------------------------------------------*/\n\nconst OVERLAY_NAME = 'DialogOverlay';\n\ntype DialogOverlayElement = DialogOverlayImplElement;\ninterface DialogOverlayProps extends DialogOverlayImplProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst DialogOverlay = React.forwardRef<DialogOverlayElement, DialogOverlayProps>(\n  (props: ScopedProps<DialogOverlayProps>, forwardedRef) => {\n    const portalContext = usePortalContext(OVERLAY_NAME, props.__scopeDialog);\n    const { forceMount = portalContext.forceMount, ...overlayProps } = props;\n    const context = useDialogContext(OVERLAY_NAME, props.__scopeDialog);\n    return context.modal ? (\n      <Presence present={forceMount || context.open}>\n        <DialogOverlayImpl {...overlayProps} ref={forwardedRef} />\n      </Presence>\n    ) : null;\n  }\n);\n\nDialogOverlay.displayName = OVERLAY_NAME;\n\ntype DialogOverlayImplElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface DialogOverlayImplProps extends PrimitiveDivProps {}\n\nconst Slot = createSlot('DialogOverlay.RemoveScroll');\n\nconst DialogOverlayImpl = React.forwardRef<DialogOverlayImplElement, DialogOverlayImplProps>(\n  (props: ScopedProps<DialogOverlayImplProps>, forwardedRef) => {\n    const { __scopeDialog, ...overlayProps } = props;\n    const context = useDialogContext(OVERLAY_NAME, __scopeDialog);\n    return (\n      // Make sure `Content` is scrollable even when it doesn't live inside `RemoveScroll`\n      // ie. when `Overlay` and `Content` are siblings\n      <RemoveScroll as={Slot} allowPinchZoom shards={[context.contentRef]}>\n        <Primitive.div\n          data-state={getState(context.open)}\n          {...overlayProps}\n          ref={forwardedRef}\n          // We re-enable pointer-events prevented by `Dialog.Content` to allow scrolling the overlay.\n          style={{ pointerEvents: 'auto', ...overlayProps.style }}\n        />\n      </RemoveScroll>\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * DialogContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'DialogContent';\n\ntype DialogContentElement = DialogContentTypeElement;\ninterface DialogContentProps extends DialogContentTypeProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst DialogContent = React.forwardRef<DialogContentElement, DialogContentProps>(\n  (props: ScopedProps<DialogContentProps>, forwardedRef) => {\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeDialog);\n    const { forceMount = portalContext.forceMount, ...contentProps } = props;\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    return (\n      <Presence present={forceMount || context.open}>\n        {context.modal ? (\n          <DialogContentModal {...contentProps} ref={forwardedRef} />\n        ) : (\n          <DialogContentNonModal {...contentProps} ref={forwardedRef} />\n        )}\n      </Presence>\n    );\n  }\n);\n\nDialogContent.displayName = CONTENT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype DialogContentTypeElement = DialogContentImplElement;\ninterface DialogContentTypeProps\n  extends Omit<DialogContentImplProps, 'trapFocus' | 'disableOutsidePointerEvents'> {}\n\nconst DialogContentModal = React.forwardRef<DialogContentTypeElement, DialogContentTypeProps>(\n  (props: ScopedProps<DialogContentTypeProps>, forwardedRef) => {\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    const contentRef = React.useRef<HTMLDivElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, context.contentRef, contentRef);\n\n    // aria-hide everything except the content (better supported equivalent to setting aria-modal)\n    React.useEffect(() => {\n      const content = contentRef.current;\n      if (content) return hideOthers(content);\n    }, []);\n\n    return (\n      <DialogContentImpl\n        {...props}\n        ref={composedRefs}\n        // we make sure focus isn't trapped once `DialogContent` has been closed\n        // (closed !== unmounted when animating out)\n        trapFocus={context.open}\n        disableOutsidePointerEvents\n        onCloseAutoFocus={composeEventHandlers(props.onCloseAutoFocus, (event) => {\n          event.preventDefault();\n          context.triggerRef.current?.focus();\n        })}\n        onPointerDownOutside={composeEventHandlers(props.onPointerDownOutside, (event) => {\n          const originalEvent = event.detail.originalEvent;\n          const ctrlLeftClick = originalEvent.button === 0 && originalEvent.ctrlKey === true;\n          const isRightClick = originalEvent.button === 2 || ctrlLeftClick;\n\n          // If the event is a right-click, we shouldn't close because\n          // it is effectively as if we right-clicked the `Overlay`.\n          if (isRightClick) event.preventDefault();\n        })}\n        // When focus is trapped, a `focusout` event may still happen.\n        // We make sure we don't trigger our `onDismiss` in such case.\n        onFocusOutside={composeEventHandlers(props.onFocusOutside, (event) =>\n          event.preventDefault()\n        )}\n      />\n    );\n  }\n);\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst DialogContentNonModal = React.forwardRef<DialogContentTypeElement, DialogContentTypeProps>(\n  (props: ScopedProps<DialogContentTypeProps>, forwardedRef) => {\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    const hasInteractedOutsideRef = React.useRef(false);\n    const hasPointerDownOutsideRef = React.useRef(false);\n\n    return (\n      <DialogContentImpl\n        {...props}\n        ref={forwardedRef}\n        trapFocus={false}\n        disableOutsidePointerEvents={false}\n        onCloseAutoFocus={(event) => {\n          props.onCloseAutoFocus?.(event);\n\n          if (!event.defaultPrevented) {\n            if (!hasInteractedOutsideRef.current) context.triggerRef.current?.focus();\n            // Always prevent auto focus because we either focus manually or want user agent focus\n            event.preventDefault();\n          }\n\n          hasInteractedOutsideRef.current = false;\n          hasPointerDownOutsideRef.current = false;\n        }}\n        onInteractOutside={(event) => {\n          props.onInteractOutside?.(event);\n\n          if (!event.defaultPrevented) {\n            hasInteractedOutsideRef.current = true;\n            if (event.detail.originalEvent.type === 'pointerdown') {\n              hasPointerDownOutsideRef.current = true;\n            }\n          }\n\n          // Prevent dismissing when clicking the trigger.\n          // As the trigger is already setup to close, without doing so would\n          // cause it to close and immediately open.\n          const target = event.target as HTMLElement;\n          const targetIsTrigger = context.triggerRef.current?.contains(target);\n          if (targetIsTrigger) event.preventDefault();\n\n          // On Safari if the trigger is inside a container with tabIndex={0}, when clicked\n          // we will get the pointer down outside event on the trigger, but then a subsequent\n          // focus outside event on the container, we ignore any focus outside event when we've\n          // already had a pointer down outside event.\n          if (event.detail.originalEvent.type === 'focusin' && hasPointerDownOutsideRef.current) {\n            event.preventDefault();\n          }\n        }}\n      />\n    );\n  }\n);\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype DialogContentImplElement = React.ComponentRef<typeof DismissableLayer>;\ntype DismissableLayerProps = React.ComponentPropsWithoutRef<typeof DismissableLayer>;\ntype FocusScopeProps = React.ComponentPropsWithoutRef<typeof FocusScope>;\ninterface DialogContentImplProps extends Omit<DismissableLayerProps, 'onDismiss'> {\n  /**\n   * When `true`, focus cannot escape the `Content` via keyboard,\n   * pointer, or a programmatic focus.\n   * @defaultValue false\n   */\n  trapFocus?: FocusScopeProps['trapped'];\n\n  /**\n   * Event handler called when auto-focusing on open.\n   * Can be prevented.\n   */\n  onOpenAutoFocus?: FocusScopeProps['onMountAutoFocus'];\n\n  /**\n   * Event handler called when auto-focusing on close.\n   * Can be prevented.\n   */\n  onCloseAutoFocus?: FocusScopeProps['onUnmountAutoFocus'];\n}\n\nconst DialogContentImpl = React.forwardRef<DialogContentImplElement, DialogContentImplProps>(\n  (props: ScopedProps<DialogContentImplProps>, forwardedRef) => {\n    const { __scopeDialog, trapFocus, onOpenAutoFocus, onCloseAutoFocus, ...contentProps } = props;\n    const context = useDialogContext(CONTENT_NAME, __scopeDialog);\n    const contentRef = React.useRef<HTMLDivElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, contentRef);\n\n    // Make sure the whole tree has focus guards as our `Dialog` will be\n    // the last element in the DOM (because of the `Portal`)\n    useFocusGuards();\n\n    return (\n      <>\n        <FocusScope\n          asChild\n          loop\n          trapped={trapFocus}\n          onMountAutoFocus={onOpenAutoFocus}\n          onUnmountAutoFocus={onCloseAutoFocus}\n        >\n          <DismissableLayer\n            role=\"dialog\"\n            id={context.contentId}\n            aria-describedby={context.descriptionId}\n            aria-labelledby={context.titleId}\n            data-state={getState(context.open)}\n            {...contentProps}\n            ref={composedRefs}\n            onDismiss={() => context.onOpenChange(false)}\n          />\n        </FocusScope>\n        {process.env.NODE_ENV !== 'production' && (\n          <>\n            <TitleWarning titleId={context.titleId} />\n            <DescriptionWarning contentRef={contentRef} descriptionId={context.descriptionId} />\n          </>\n        )}\n      </>\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * DialogTitle\n * -----------------------------------------------------------------------------------------------*/\n\nconst TITLE_NAME = 'DialogTitle';\n\ntype DialogTitleElement = React.ComponentRef<typeof Primitive.h2>;\ntype PrimitiveHeading2Props = React.ComponentPropsWithoutRef<typeof Primitive.h2>;\ninterface DialogTitleProps extends PrimitiveHeading2Props {}\n\nconst DialogTitle = React.forwardRef<DialogTitleElement, DialogTitleProps>(\n  (props: ScopedProps<DialogTitleProps>, forwardedRef) => {\n    const { __scopeDialog, ...titleProps } = props;\n    const context = useDialogContext(TITLE_NAME, __scopeDialog);\n    return <Primitive.h2 id={context.titleId} {...titleProps} ref={forwardedRef} />;\n  }\n);\n\nDialogTitle.displayName = TITLE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogDescription\n * -----------------------------------------------------------------------------------------------*/\n\nconst DESCRIPTION_NAME = 'DialogDescription';\n\ntype DialogDescriptionElement = React.ComponentRef<typeof Primitive.p>;\ntype PrimitiveParagraphProps = React.ComponentPropsWithoutRef<typeof Primitive.p>;\ninterface DialogDescriptionProps extends PrimitiveParagraphProps {}\n\nconst DialogDescription = React.forwardRef<DialogDescriptionElement, DialogDescriptionProps>(\n  (props: ScopedProps<DialogDescriptionProps>, forwardedRef) => {\n    const { __scopeDialog, ...descriptionProps } = props;\n    const context = useDialogContext(DESCRIPTION_NAME, __scopeDialog);\n    return <Primitive.p id={context.descriptionId} {...descriptionProps} ref={forwardedRef} />;\n  }\n);\n\nDialogDescription.displayName = DESCRIPTION_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogClose\n * -----------------------------------------------------------------------------------------------*/\n\nconst CLOSE_NAME = 'DialogClose';\n\ntype DialogCloseElement = React.ComponentRef<typeof Primitive.button>;\ninterface DialogCloseProps extends PrimitiveButtonProps {}\n\nconst DialogClose = React.forwardRef<DialogCloseElement, DialogCloseProps>(\n  (props: ScopedProps<DialogCloseProps>, forwardedRef) => {\n    const { __scopeDialog, ...closeProps } = props;\n    const context = useDialogContext(CLOSE_NAME, __scopeDialog);\n    return (\n      <Primitive.button\n        type=\"button\"\n        {...closeProps}\n        ref={forwardedRef}\n        onClick={composeEventHandlers(props.onClick, () => context.onOpenChange(false))}\n      />\n    );\n  }\n);\n\nDialogClose.displayName = CLOSE_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction getState(open: boolean) {\n  return open ? 'open' : 'closed';\n}\n\nconst TITLE_WARNING_NAME = 'DialogTitleWarning';\n\nconst [WarningProvider, useWarningContext] = createContext(TITLE_WARNING_NAME, {\n  contentName: CONTENT_NAME,\n  titleName: TITLE_NAME,\n  docsSlug: 'dialog',\n});\n\ntype TitleWarningProps = { titleId?: string };\n\nconst TitleWarning: React.FC<TitleWarningProps> = ({ titleId }) => {\n  const titleWarningContext = useWarningContext(TITLE_WARNING_NAME);\n\n  const MESSAGE = `\\`${titleWarningContext.contentName}\\` requires a \\`${titleWarningContext.titleName}\\` for the component to be accessible for screen reader users.\n\nIf you want to hide the \\`${titleWarningContext.titleName}\\`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/${titleWarningContext.docsSlug}`;\n\n  React.useEffect(() => {\n    if (titleId) {\n      const hasTitle = document.getElementById(titleId);\n      if (!hasTitle) console.error(MESSAGE);\n    }\n  }, [MESSAGE, titleId]);\n\n  return null;\n};\n\nconst DESCRIPTION_WARNING_NAME = 'DialogDescriptionWarning';\n\ntype DescriptionWarningProps = {\n  contentRef: React.RefObject<DialogContentElement | null>;\n  descriptionId?: string;\n};\n\nconst DescriptionWarning: React.FC<DescriptionWarningProps> = ({ contentRef, descriptionId }) => {\n  const descriptionWarningContext = useWarningContext(DESCRIPTION_WARNING_NAME);\n  const MESSAGE = `Warning: Missing \\`Description\\` or \\`aria-describedby={undefined}\\` for {${descriptionWarningContext.contentName}}.`;\n\n  React.useEffect(() => {\n    const describedById = contentRef.current?.getAttribute('aria-describedby');\n    // if we have an id and the user hasn't set aria-describedby={undefined}\n    if (descriptionId && describedById) {\n      const hasDescription = document.getElementById(descriptionId);\n      if (!hasDescription) console.warn(MESSAGE);\n    }\n  }, [MESSAGE, contentRef, descriptionId]);\n\n  return null;\n};\n\nconst Root = Dialog;\nconst Trigger = DialogTrigger;\nconst Portal = DialogPortal;\nconst Overlay = DialogOverlay;\nconst Content = DialogContent;\nconst Title = DialogTitle;\nconst Description = DialogDescription;\nconst Close = DialogClose;\n\nexport {\n  createDialogScope,\n  //\n  Dialog,\n  DialogTrigger,\n  DialogPortal,\n  DialogOverlay,\n  DialogContent,\n  DialogTitle,\n  DialogDescription,\n  DialogClose,\n  //\n  Root,\n  Trigger,\n  Portal,\n  Overlay,\n  Content,\n  Title,\n  Description,\n  Close,\n  //\n  WarningProvider,\n};\nexport type {\n  DialogProps,\n  DialogTriggerProps,\n  DialogPortalProps,\n  DialogOverlayProps,\n  DialogContentProps,\n  DialogTitleProps,\n  DialogDescriptionProps,\n  DialogCloseProps,\n};\n"], "mappings": ";;;AAAA,YAAY,WAAW;AACvB,SAAS,4BAA4B;AACrC,SAAS,uBAAuB;AAChC,SAAS,eAAe,0BAA0B;AAClD,SAAS,aAAa;AACtB,SAAS,4BAA4B;AACrC,SAAS,wBAAwB;AACjC,SAAS,kBAAkB;AAC3B,SAAS,UAAU,uBAAuB;AAC1C,SAAS,gBAAgB;AACzB,SAAS,iBAAiB;AAC1B,SAAS,sBAAsB;AAC/B,SAAS,oBAAoB;AAC7B,SAAS,kBAAkB;AAC3B,SAAS,kBAAkB;AAsDvB,SA2VM,UA3VN,KA2VM,YA3VN;AA9CJ,IAAM,cAAc;AAGpB,IAAM,CAAC,qBAAqB,iBAAiB,IAAI,mBAAmB,WAAW;AAc/E,IAAM,CAAC,gBAAgB,gBAAgB,IAAI,oBAAwC,WAAW;AAU9F,IAAM,SAAgC,CAAC,UAAoC;AACzE,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,MAAM;AAAA,IACN;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,EACV,IAAI;AACJ,QAAM,aAAmB,aAA0B,IAAI;AACvD,QAAM,aAAmB,aAA6B,IAAI;AAC1D,QAAM,CAAC,MAAM,OAAO,IAAI,qBAAqB;AAAA,IAC3C,MAAM;AAAA,IACN,aAAa,eAAe;AAAA,IAC5B,UAAU;AAAA,IACV,QAAQ;AAAA,EACV,CAAC;AAED,SACE;AAAA,IAAC;AAAA;AAAA,MACC,OAAO;AAAA,MACP;AAAA,MACA;AAAA,MACA,WAAW,MAAM;AAAA,MACjB,SAAS,MAAM;AAAA,MACf,eAAe,MAAM;AAAA,MACrB;AAAA,MACA,cAAc;AAAA,MACd,cAAoB,kBAAY,MAAM,QAAQ,CAAC,aAAa,CAAC,QAAQ,GAAG,CAAC,OAAO,CAAC;AAAA,MACjF;AAAA,MAEC;AAAA;AAAA,EACH;AAEJ;AAEA,OAAO,cAAc;AAMrB,IAAM,eAAe;AAMrB,IAAM,gBAAsB;AAAA,EAC1B,CAAC,OAAwC,iBAAiB;AACxD,UAAM,EAAE,eAAe,GAAG,aAAa,IAAI;AAC3C,UAAM,UAAU,iBAAiB,cAAc,aAAa;AAC5D,UAAM,qBAAqB,gBAAgB,cAAc,QAAQ,UAAU;AAC3E,WACE;AAAA,MAAC,UAAU;AAAA,MAAV;AAAA,QACC,MAAK;AAAA,QACL,iBAAc;AAAA,QACd,iBAAe,QAAQ;AAAA,QACvB,iBAAe,QAAQ;AAAA,QACvB,cAAY,SAAS,QAAQ,IAAI;AAAA,QAChC,GAAG;AAAA,QACJ,KAAK;AAAA,QACL,SAAS,qBAAqB,MAAM,SAAS,QAAQ,YAAY;AAAA;AAAA,IACnE;AAAA,EAEJ;AACF;AAEA,cAAc,cAAc;AAM5B,IAAM,cAAc;AAGpB,IAAM,CAAC,gBAAgB,gBAAgB,IAAI,oBAAwC,aAAa;AAAA,EAC9F,YAAY;AACd,CAAC;AAgBD,IAAM,eAA4C,CAAC,UAA0C;AAC3F,QAAM,EAAE,eAAe,YAAY,UAAU,UAAU,IAAI;AAC3D,QAAM,UAAU,iBAAiB,aAAa,aAAa;AAC3D,SACE,oBAAC,kBAAe,OAAO,eAAe,YACnC,UAAM,eAAS,IAAI,UAAU,CAAC,UAC7B,oBAAC,YAAS,SAAS,cAAc,QAAQ,MACvC,8BAAC,mBAAgB,SAAO,MAAC,WACtB,iBACH,GACF,CACD,GACH;AAEJ;AAEA,aAAa,cAAc;AAM3B,IAAM,eAAe;AAWrB,IAAM,gBAAsB;AAAA,EAC1B,CAAC,OAAwC,iBAAiB;AACxD,UAAM,gBAAgB,iBAAiB,cAAc,MAAM,aAAa;AACxE,UAAM,EAAE,aAAa,cAAc,YAAY,GAAG,aAAa,IAAI;AACnE,UAAM,UAAU,iBAAiB,cAAc,MAAM,aAAa;AAClE,WAAO,QAAQ,QACb,oBAAC,YAAS,SAAS,cAAc,QAAQ,MACvC,8BAAC,qBAAmB,GAAG,cAAc,KAAK,cAAc,GAC1D,IACE;AAAA,EACN;AACF;AAEA,cAAc,cAAc;AAM5B,IAAM,OAAO,WAAW,4BAA4B;AAEpD,IAAM,oBAA0B;AAAA,EAC9B,CAAC,OAA4C,iBAAiB;AAC5D,UAAM,EAAE,eAAe,GAAG,aAAa,IAAI;AAC3C,UAAM,UAAU,iBAAiB,cAAc,aAAa;AAC5D;AAAA;AAAA;AAAA,MAGE,oBAAC,gBAAa,IAAI,MAAM,gBAAc,MAAC,QAAQ,CAAC,QAAQ,UAAU,GAChE;AAAA,QAAC,UAAU;AAAA,QAAV;AAAA,UACC,cAAY,SAAS,QAAQ,IAAI;AAAA,UAChC,GAAG;AAAA,UACJ,KAAK;AAAA,UAEL,OAAO,EAAE,eAAe,QAAQ,GAAG,aAAa,MAAM;AAAA;AAAA,MACxD,GACF;AAAA;AAAA,EAEJ;AACF;AAMA,IAAM,eAAe;AAWrB,IAAM,gBAAsB;AAAA,EAC1B,CAAC,OAAwC,iBAAiB;AACxD,UAAM,gBAAgB,iBAAiB,cAAc,MAAM,aAAa;AACxE,UAAM,EAAE,aAAa,cAAc,YAAY,GAAG,aAAa,IAAI;AACnE,UAAM,UAAU,iBAAiB,cAAc,MAAM,aAAa;AAClE,WACE,oBAAC,YAAS,SAAS,cAAc,QAAQ,MACtC,kBAAQ,QACP,oBAAC,sBAAoB,GAAG,cAAc,KAAK,cAAc,IAEzD,oBAAC,yBAAuB,GAAG,cAAc,KAAK,cAAc,GAEhE;AAAA,EAEJ;AACF;AAEA,cAAc,cAAc;AAQ5B,IAAM,qBAA2B;AAAA,EAC/B,CAAC,OAA4C,iBAAiB;AAC5D,UAAM,UAAU,iBAAiB,cAAc,MAAM,aAAa;AAClE,UAAM,aAAmB,aAAuB,IAAI;AACpD,UAAM,eAAe,gBAAgB,cAAc,QAAQ,YAAY,UAAU;AAGjF,IAAM,gBAAU,MAAM;AACpB,YAAM,UAAU,WAAW;AAC3B,UAAI,QAAS,QAAO,WAAW,OAAO;AAAA,IACxC,GAAG,CAAC,CAAC;AAEL,WACE;AAAA,MAAC;AAAA;AAAA,QACE,GAAG;AAAA,QACJ,KAAK;AAAA,QAGL,WAAW,QAAQ;AAAA,QACnB,6BAA2B;AAAA,QAC3B,kBAAkB,qBAAqB,MAAM,kBAAkB,CAAC,UAAU;AACxE,gBAAM,eAAe;AACrB,kBAAQ,WAAW,SAAS,MAAM;AAAA,QACpC,CAAC;AAAA,QACD,sBAAsB,qBAAqB,MAAM,sBAAsB,CAAC,UAAU;AAChF,gBAAM,gBAAgB,MAAM,OAAO;AACnC,gBAAM,gBAAgB,cAAc,WAAW,KAAK,cAAc,YAAY;AAC9E,gBAAM,eAAe,cAAc,WAAW,KAAK;AAInD,cAAI,aAAc,OAAM,eAAe;AAAA,QACzC,CAAC;AAAA,QAGD,gBAAgB;AAAA,UAAqB,MAAM;AAAA,UAAgB,CAAC,UAC1D,MAAM,eAAe;AAAA,QACvB;AAAA;AAAA,IACF;AAAA,EAEJ;AACF;AAIA,IAAM,wBAA8B;AAAA,EAClC,CAAC,OAA4C,iBAAiB;AAC5D,UAAM,UAAU,iBAAiB,cAAc,MAAM,aAAa;AAClE,UAAM,0BAAgC,aAAO,KAAK;AAClD,UAAM,2BAAiC,aAAO,KAAK;AAEnD,WACE;AAAA,MAAC;AAAA;AAAA,QACE,GAAG;AAAA,QACJ,KAAK;AAAA,QACL,WAAW;AAAA,QACX,6BAA6B;AAAA,QAC7B,kBAAkB,CAAC,UAAU;AAC3B,gBAAM,mBAAmB,KAAK;AAE9B,cAAI,CAAC,MAAM,kBAAkB;AAC3B,gBAAI,CAAC,wBAAwB,QAAS,SAAQ,WAAW,SAAS,MAAM;AAExE,kBAAM,eAAe;AAAA,UACvB;AAEA,kCAAwB,UAAU;AAClC,mCAAyB,UAAU;AAAA,QACrC;AAAA,QACA,mBAAmB,CAAC,UAAU;AAC5B,gBAAM,oBAAoB,KAAK;AAE/B,cAAI,CAAC,MAAM,kBAAkB;AAC3B,oCAAwB,UAAU;AAClC,gBAAI,MAAM,OAAO,cAAc,SAAS,eAAe;AACrD,uCAAyB,UAAU;AAAA,YACrC;AAAA,UACF;AAKA,gBAAM,SAAS,MAAM;AACrB,gBAAM,kBAAkB,QAAQ,WAAW,SAAS,SAAS,MAAM;AACnE,cAAI,gBAAiB,OAAM,eAAe;AAM1C,cAAI,MAAM,OAAO,cAAc,SAAS,aAAa,yBAAyB,SAAS;AACrF,kBAAM,eAAe;AAAA,UACvB;AAAA,QACF;AAAA;AAAA,IACF;AAAA,EAEJ;AACF;AA4BA,IAAM,oBAA0B;AAAA,EAC9B,CAAC,OAA4C,iBAAiB;AAC5D,UAAM,EAAE,eAAe,WAAW,iBAAiB,kBAAkB,GAAG,aAAa,IAAI;AACzF,UAAM,UAAU,iBAAiB,cAAc,aAAa;AAC5D,UAAM,aAAmB,aAAuB,IAAI;AACpD,UAAM,eAAe,gBAAgB,cAAc,UAAU;AAI7D,mBAAe;AAEf,WACE,iCACE;AAAA;AAAA,QAAC;AAAA;AAAA,UACC,SAAO;AAAA,UACP,MAAI;AAAA,UACJ,SAAS;AAAA,UACT,kBAAkB;AAAA,UAClB,oBAAoB;AAAA,UAEpB;AAAA,YAAC;AAAA;AAAA,cACC,MAAK;AAAA,cACL,IAAI,QAAQ;AAAA,cACZ,oBAAkB,QAAQ;AAAA,cAC1B,mBAAiB,QAAQ;AAAA,cACzB,cAAY,SAAS,QAAQ,IAAI;AAAA,cAChC,GAAG;AAAA,cACJ,KAAK;AAAA,cACL,WAAW,MAAM,QAAQ,aAAa,KAAK;AAAA;AAAA,UAC7C;AAAA;AAAA,MACF;AAAA,MAEE,iCACE;AAAA,4BAAC,gBAAa,SAAS,QAAQ,SAAS;AAAA,QACxC,oBAAC,sBAAmB,YAAwB,eAAe,QAAQ,eAAe;AAAA,SACpF;AAAA,OAEJ;AAAA,EAEJ;AACF;AAMA,IAAM,aAAa;AAMnB,IAAM,cAAoB;AAAA,EACxB,CAAC,OAAsC,iBAAiB;AACtD,UAAM,EAAE,eAAe,GAAG,WAAW,IAAI;AACzC,UAAM,UAAU,iBAAiB,YAAY,aAAa;AAC1D,WAAO,oBAAC,UAAU,IAAV,EAAa,IAAI,QAAQ,SAAU,GAAG,YAAY,KAAK,cAAc;AAAA,EAC/E;AACF;AAEA,YAAY,cAAc;AAM1B,IAAM,mBAAmB;AAMzB,IAAM,oBAA0B;AAAA,EAC9B,CAAC,OAA4C,iBAAiB;AAC5D,UAAM,EAAE,eAAe,GAAG,iBAAiB,IAAI;AAC/C,UAAM,UAAU,iBAAiB,kBAAkB,aAAa;AAChE,WAAO,oBAAC,UAAU,GAAV,EAAY,IAAI,QAAQ,eAAgB,GAAG,kBAAkB,KAAK,cAAc;AAAA,EAC1F;AACF;AAEA,kBAAkB,cAAc;AAMhC,IAAM,aAAa;AAKnB,IAAM,cAAoB;AAAA,EACxB,CAAC,OAAsC,iBAAiB;AACtD,UAAM,EAAE,eAAe,GAAG,WAAW,IAAI;AACzC,UAAM,UAAU,iBAAiB,YAAY,aAAa;AAC1D,WACE;AAAA,MAAC,UAAU;AAAA,MAAV;AAAA,QACC,MAAK;AAAA,QACJ,GAAG;AAAA,QACJ,KAAK;AAAA,QACL,SAAS,qBAAqB,MAAM,SAAS,MAAM,QAAQ,aAAa,KAAK,CAAC;AAAA;AAAA,IAChF;AAAA,EAEJ;AACF;AAEA,YAAY,cAAc;AAI1B,SAAS,SAAS,MAAe;AAC/B,SAAO,OAAO,SAAS;AACzB;AAEA,IAAM,qBAAqB;AAE3B,IAAM,CAAC,iBAAiB,iBAAiB,IAAI,cAAc,oBAAoB;AAAA,EAC7E,aAAa;AAAA,EACb,WAAW;AAAA,EACX,UAAU;AACZ,CAAC;AAID,IAAM,eAA4C,CAAC,EAAE,QAAQ,MAAM;AACjE,QAAM,sBAAsB,kBAAkB,kBAAkB;AAEhE,QAAM,UAAU,KAAK,oBAAoB,WAAW,mBAAmB,oBAAoB,SAAS;AAAA;AAAA,4BAE1E,oBAAoB,SAAS;AAAA;AAAA,4EAEmB,oBAAoB,QAAQ;AAEtG,EAAM,gBAAU,MAAM;AACpB,QAAI,SAAS;AACX,YAAM,WAAW,SAAS,eAAe,OAAO;AAChD,UAAI,CAAC,SAAU,SAAQ,MAAM,OAAO;AAAA,IACtC;AAAA,EACF,GAAG,CAAC,SAAS,OAAO,CAAC;AAErB,SAAO;AACT;AAEA,IAAM,2BAA2B;AAOjC,IAAM,qBAAwD,CAAC,EAAE,YAAY,cAAc,MAAM;AAC/F,QAAM,4BAA4B,kBAAkB,wBAAwB;AAC5E,QAAM,UAAU,6EAA6E,0BAA0B,WAAW;AAElI,EAAM,gBAAU,MAAM;AACpB,UAAM,gBAAgB,WAAW,SAAS,aAAa,kBAAkB;AAEzE,QAAI,iBAAiB,eAAe;AAClC,YAAM,iBAAiB,SAAS,eAAe,aAAa;AAC5D,UAAI,CAAC,eAAgB,SAAQ,KAAK,OAAO;AAAA,IAC3C;AAAA,EACF,GAAG,CAAC,SAAS,YAAY,aAAa,CAAC;AAEvC,SAAO;AACT;AAEA,IAAM,OAAO;AACb,IAAM,UAAU;AAChB,IAAM,SAAS;AACf,IAAM,UAAU;AAChB,IAAM,UAAU;AAChB,IAAM,QAAQ;AACd,IAAM,cAAc;AACpB,IAAM,QAAQ;", "names": []}