'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { api } from '@/lib/api'
import { toast } from 'sonner'
import { Mail, Lock, TestTube } from 'lucide-react'

export default function TestAuthPage() {
  const [email, setEmail] = useState('')
  const [token, setToken] = useState('')
  const [password, setPassword] = useState('')
  const [loading, setLoading] = useState(false)

  const testSendVerification = async () => {
    if (!email) {
      toast.error('Please enter an email')
      return
    }
    
    setLoading(true)
    try {
      const response = await api.sendVerificationEmail(email)
      toast.success('Verification email sent! Check the server logs for the token.')
      console.log('Verification response:', response)
    } catch (error: any) {
      toast.error(error.message || 'Failed to send verification email')
    } finally {
      setLoading(false)
    }
  }

  const testVerifyEmail = async () => {
    if (!token) {
      toast.error('Please enter a token')
      return
    }
    
    setLoading(true)
    try {
      const response = await api.verifyEmail(token)
      toast.success('Email verified successfully!')
      console.log('Verify response:', response)
    } catch (error: any) {
      toast.error(error.message || 'Failed to verify email')
    } finally {
      setLoading(false)
    }
  }

  const testForgotPassword = async () => {
    if (!email) {
      toast.error('Please enter an email')
      return
    }
    
    setLoading(true)
    try {
      const response = await api.forgotPassword(email)
      toast.success('Password reset email sent! Check the server logs for the token.')
      console.log('Forgot password response:', response)
    } catch (error: any) {
      toast.error(error.message || 'Failed to send reset email')
    } finally {
      setLoading(false)
    }
  }

  const testResetPassword = async () => {
    if (!token || !password) {
      toast.error('Please enter both token and new password')
      return
    }
    
    setLoading(true)
    try {
      const response = await api.resetPassword(token, password)
      toast.success('Password reset successfully!')
      console.log('Reset password response:', response)
    } catch (error: any) {
      toast.error(error.message || 'Failed to reset password')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-muted/10 to-secondary/5 p-8">
      <div className="max-w-4xl mx-auto space-y-8">
        <div className="text-center space-y-4">
          <div className="w-16 h-16 rounded-3xl bg-gradient-to-br from-primary to-accent flex items-center justify-center mx-auto">
            <TestTube className="h-8 w-8 text-white" />
          </div>
          <h1 className="text-4xl font-black text-foreground">
            Auth Testing Dashboard 🧪
          </h1>
          <p className="text-muted-foreground">
            Test email verification and password reset functionality
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-8">
          {/* Email Verification Tests */}
          <Card className="glass border-0 rounded-3xl">
            <CardHeader className="bg-gradient-to-r from-primary/5 to-accent/5 border-b border-border/50">
              <CardTitle className="flex items-center gap-3">
                <Mail className="h-6 w-6 text-primary" />
                Email Verification
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6 space-y-6">
              <div>
                <label className="text-sm font-bold text-foreground mb-3 block">
                  Email Address
                </label>
                <Input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  className="rounded-2xl border-2 h-12 font-medium"
                />
              </div>
              
              <div>
                <label className="text-sm font-bold text-foreground mb-3 block">
                  Verification Token
                </label>
                <Input
                  value={token}
                  onChange={(e) => setToken(e.target.value)}
                  placeholder="Enter token from server logs"
                  className="rounded-2xl border-2 h-12 font-medium"
                />
              </div>

              <div className="space-y-3">
                <Button
                  onClick={testSendVerification}
                  disabled={loading}
                  className="w-full rounded-2xl font-bold h-12 gradient-primary"
                >
                  Send Verification Email
                </Button>
                <Button
                  onClick={testVerifyEmail}
                  disabled={loading}
                  variant="outline"
                  className="w-full rounded-2xl font-bold h-12"
                >
                  Verify Email
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Password Reset Tests */}
          <Card className="glass border-0 rounded-3xl">
            <CardHeader className="bg-gradient-to-r from-destructive/5 to-orange-500/5 border-b border-border/50">
              <CardTitle className="flex items-center gap-3">
                <Lock className="h-6 w-6 text-destructive" />
                Password Reset
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6 space-y-6">
              <div>
                <label className="text-sm font-bold text-foreground mb-3 block">
                  Email Address
                </label>
                <Input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  className="rounded-2xl border-2 h-12 font-medium"
                />
              </div>
              
              <div>
                <label className="text-sm font-bold text-foreground mb-3 block">
                  Reset Token
                </label>
                <Input
                  value={token}
                  onChange={(e) => setToken(e.target.value)}
                  placeholder="Enter token from server logs"
                  className="rounded-2xl border-2 h-12 font-medium"
                />
              </div>

              <div>
                <label className="text-sm font-bold text-foreground mb-3 block">
                  New Password
                </label>
                <Input
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Enter new password"
                  className="rounded-2xl border-2 h-12 font-medium"
                />
              </div>

              <div className="space-y-3">
                <Button
                  onClick={testForgotPassword}
                  disabled={loading}
                  className="w-full rounded-2xl font-bold h-12 bg-gradient-to-r from-destructive to-orange-500"
                >
                  Send Reset Email
                </Button>
                <Button
                  onClick={testResetPassword}
                  disabled={loading}
                  variant="outline"
                  className="w-full rounded-2xl font-bold h-12"
                >
                  Reset Password
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        <Card className="glass border-0 rounded-3xl">
          <CardContent className="p-6">
            <h3 className="font-bold text-foreground mb-4">📝 Instructions:</h3>
            <ol className="space-y-2 text-muted-foreground">
              <li>1. Enter an email address that exists in your database</li>
              <li>2. Click "Send Verification Email" or "Send Reset Email"</li>
              <li>3. Check your server console logs for the generated token</li>
              <li>4. Copy the token and paste it in the token field</li>
              <li>5. Click "Verify Email" or "Reset Password" to test the functionality</li>
            </ol>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
