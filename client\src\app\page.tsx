'use client'

import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useQuery } from '@tanstack/react-query'
import { api, Job } from '@/lib/api'
import {
  Search,
  MapPin,
  Clock,
  Users,
  TrendingUp,
  Briefcase,
  Star,
  ArrowRight,
  CheckCircle
} from 'lucide-react'

export default function HomePage() {
  // Fetch featured jobs
  const { data: featuredJobsData, isLoading, error } = useQuery({
    queryKey: ['featured-jobs'],
    queryFn: () => api.getFeaturedJobs(),
    retry: 1,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })

  // Ensure featuredJobs is always an array
  const featuredJobs = Array.isArray(featuredJobsData) ? featuredJobsData : []

  const stats = [
    { label: 'Active Jobs', value: '1,200+', icon: Briefcase },
    { label: 'Companies', value: '500+', icon: Users },
    { label: 'Success Stories', value: '10,000+', icon: Star },
    { label: 'Growth Rate', value: '25%', icon: TrendingUp },
  ]

  const features = [
    {
      title: 'Smart Job Matching',
      description: 'Our AI-powered algorithm matches you with jobs that fit your skills and preferences.',
      icon: Search,
    },
    {
      title: 'Verified Companies',
      description: 'All companies on our platform are verified to ensure legitimate opportunities.',
      icon: CheckCircle,
    },
    {
      title: 'Career Growth',
      description: 'Access resources and tools to advance your career and develop new skills.',
      icon: TrendingUp,
    },
  ]

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative py-24 lg:py-40 overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-background via-muted/20 to-secondary/10"></div>
        <div className="absolute top-20 left-10 w-72 h-72 bg-primary/10 rounded-full blur-3xl float"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-accent/10 rounded-full blur-3xl float" style={{animationDelay: '2s'}}></div>

        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative">
          <div className="max-w-5xl mx-auto text-center">
            <div className="mb-8">
              <span className="inline-block px-4 py-2 bg-primary/10 text-primary font-semibold rounded-full text-sm mb-6">
                ✨ Your career starts here
              </span>
            </div>
            <h1 className="text-5xl md:text-7xl lg:text-8xl font-bold text-foreground mb-8 leading-tight">
              Find Your{' '}
              <span className="gradient-primary bg-clip-text text-transparent">
                Next Opportunity
              </span>
              <br />
              <span className="text-4xl md:text-5xl lg:text-6xl text-muted-foreground font-medium">
                Today
              </span>
            </h1>
            <p className="text-xl md:text-2xl text-muted-foreground mb-12 max-w-3xl mx-auto leading-relaxed">
              Connect with leading companies and discover career opportunities that align with your goals.
              <span className="text-foreground font-semibold"> Join thousands of professionals</span> who have advanced their careers with us.
            </p>
            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
              <Button size="lg" asChild className="text-lg px-10 py-6 rounded-lg font-semibold gradient-primary shadow-lg hover:shadow-xl transition-all duration-300">
                <Link href="/jobs">
                  Explore Opportunities
                  <ArrowRight className="ml-3 h-6 w-6" />
                </Link>
              </Button>
              <Button size="lg" variant="outline" asChild className="text-lg px-10 py-6 rounded-lg font-semibold border-2 hover:bg-muted/50 transition-all duration-300">
                <Link href="/auth/register">Get Started</Link>
              </Button>
            </div>

            {/* Trust indicators */}
            <div className="mt-16 flex flex-wrap justify-center items-center gap-8 text-sm text-muted-foreground">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-accent" />
                <span>Secure & Private</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-accent" />
                <span>Always Free</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-accent" />
                <span>Verified Companies</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-6">
            {stats.map((stat, index) => {
              const Icon = stat.icon
              return (
                <Card key={index} className="text-center p-8 glass border-0 hover:shadow-lg transition-all duration-300 group">
                  <div className="mx-auto mb-6 flex h-20 w-20 items-center justify-center rounded-lg gradient-primary shadow-lg group-hover:shadow-xl transition-all duration-300">
                    <Icon className="h-10 w-10 text-primary-foreground" />
                  </div>
                  <div className="text-4xl font-bold text-foreground mb-3">{stat.value}</div>
                  <div className="text-muted-foreground font-semibold">{stat.label}</div>
                </Card>
              )
            })}
          </div>
        </div>
      </section>

      {/* Featured Jobs Section */}
      <section className="py-24 bg-muted/20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <span className="inline-block px-4 py-2 bg-accent/10 text-accent font-semibold rounded-full text-sm mb-6">
              🌟 Featured Opportunities
            </span>
            <h2 className="text-4xl md:text-6xl font-bold text-foreground mb-6">
              Premium{' '}
              <span className="gradient-accent bg-clip-text text-transparent">
                Positions
              </span>
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Carefully selected opportunities from leading companies with excellent workplace cultures.
              <span className="text-foreground font-semibold"> Quality positions from trusted employers.</span>
            </p>
          </div>

          {isLoading ? (
            <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
              {[...Array(6)].map((_, i) => (
                <Card key={i} className="animate-pulse glass border-0 p-6">
                  <CardHeader className="p-0 mb-4">
                    <div className="h-6 bg-muted rounded-xl w-3/4 mb-3"></div>
                    <div className="h-4 bg-muted rounded-lg w-1/2"></div>
                  </CardHeader>
                  <CardContent className="p-0">
                    <div className="space-y-3">
                      <div className="h-4 bg-muted rounded-lg"></div>
                      <div className="h-4 bg-muted rounded-lg w-2/3"></div>
                      <div className="h-10 bg-muted rounded-2xl mt-6"></div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <p className="text-muted-foreground mb-4">
                Unable to load featured jobs at the moment.
              </p>
              <Button variant="outline" asChild>
                <Link href="/jobs">Browse All Jobs</Link>
              </Button>
            </div>
          ) : featuredJobs.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-muted-foreground mb-4">
                No featured jobs available right now.
              </p>
              <Button variant="outline" asChild>
                <Link href="/jobs">Browse All Jobs</Link>
              </Button>
            </div>
          ) : (
            <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
              {featuredJobs.slice(0, 6).map((job: Job) => (
                <Card key={job.id} className="glass border-0 p-6 hover:scale-105 hover:glow-primary transition-all duration-300 group">
                  <CardHeader className="p-0 mb-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <CardTitle className="text-xl font-bold mb-3 group-hover:text-primary transition-colors">{job.title}</CardTitle>
                        <CardDescription className="flex items-center text-base font-semibold">
                          <Users className="h-5 w-5 mr-2" />
                          {job.companyName}
                        </CardDescription>
                      </div>
                      {job.isFeatured && (
                        <Badge className="ml-2 gradient-accent text-accent-foreground font-semibold rounded-full">
                          ⭐ Featured
                        </Badge>
                      )}
                    </div>
                  </CardHeader>
                  <CardContent className="p-0">
                    <div className="space-y-4">
                      <div className="flex items-center text-muted-foreground font-medium">
                        <MapPin className="h-5 w-5 mr-3 text-primary" />
                        {job.location} • {job.workLocationType || 'Hybrid'}
                      </div>
                      <div className="flex items-center text-muted-foreground font-medium">
                        <Clock className="h-5 w-5 mr-3 text-accent" />
                        {job.jobType} • {job.experienceLevel}
                      </div>
                      <div className="flex flex-wrap gap-3">
                        <Badge variant="outline" className="rounded-full font-semibold">{job.category}</Badge>
                        {job.salaryMin && (
                          <Badge variant="outline" className="rounded-full font-semibold text-accent border-accent/30">
                            ${job.salaryMin.toLocaleString()}
                            {job.salaryMax && `- $${job.salaryMax.toLocaleString()}`}
                          </Badge>
                        )}
                      </div>
                      <Button asChild className="w-full mt-6 rounded-2xl font-bold gradient-primary hover:scale-105 transition-all duration-200">
                        <Link href={`/jobs/${job.slug}`}>
                          Apply Now
                          <ArrowRight className="ml-2 h-5 w-5" />
                        </Link>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          <div className="text-center mt-16">
            <Button size="lg" variant="outline" asChild className="rounded-2xl font-bold border-2 px-10 py-6 hover:scale-105 transition-all duration-300">
              <Link href="/jobs">
                Browse All Jobs
                <ArrowRight className="ml-3 h-6 w-6" />
              </Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-24">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <span className="inline-block px-4 py-2 bg-secondary/10 text-secondary font-semibold rounded-full text-sm mb-6">
              ⚡ Why choose us
            </span>
            <h2 className="text-4xl md:text-6xl font-bold text-foreground mb-6">
              Built for{' '}
              <span className="gradient-primary bg-clip-text text-transparent">
                Professionals
              </span>
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              We understand the modern job market. Our platform is designed to streamline your career search with intelligent matching and professional tools.
            </p>
          </div>

          <div className="grid gap-10 md:grid-cols-3">
            {features.map((feature, index) => {
              const Icon = feature.icon
              return (
                <Card key={index} className="text-center glass border-0 p-8 hover:scale-105 transition-all duration-300 group">
                  <CardHeader className="p-0 mb-6">
                    <div className="mx-auto mb-6 flex h-24 w-24 items-center justify-center rounded-3xl gradient-accent glow-accent group-hover:scale-110 transition-all duration-300">
                      <Icon className="h-12 w-12 text-accent-foreground" />
                    </div>
                    <CardTitle className="text-2xl font-bold group-hover:text-primary transition-colors">{feature.title}</CardTitle>
                  </CardHeader>
                  <CardContent className="p-0">
                    <CardDescription className="text-lg text-muted-foreground leading-relaxed">
                      {feature.description}
                    </CardDescription>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 bg-muted/20 relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute top-10 left-10 w-64 h-64 bg-primary/5 rounded-full blur-3xl float"></div>
        <div className="absolute bottom-10 right-10 w-80 h-80 bg-accent/5 rounded-full blur-3xl float" style={{animationDelay: '3s'}}></div>

        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative">
          <div className="max-w-5xl mx-auto text-center">
            <span className="inline-block px-4 py-2 bg-primary/10 text-primary font-semibold rounded-full text-sm mb-8">
              🚀 Ready to advance your career?
            </span>
            <h2 className="text-4xl md:text-6xl lg:text-7xl font-bold text-foreground mb-8 leading-tight">
              Take the Next{' '}
              <span className="gradient-primary bg-clip-text text-transparent">
                Step Forward.
              </span>
            </h2>
            <p className="text-xl md:text-2xl text-muted-foreground mb-12 max-w-3xl mx-auto leading-relaxed">
              Your next career opportunity is waiting. Join thousands of professionals who have found success through our platform.
              <span className="text-foreground font-semibold"> Start your journey today.</span>
            </p>
            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
              <Button size="lg" asChild className="text-lg px-12 py-6 rounded-lg font-semibold gradient-primary shadow-lg hover:shadow-xl transition-all duration-300">
                <Link href="/auth/register">
                  Get Started Free
                  <ArrowRight className="ml-3 h-6 w-6" />
                </Link>
              </Button>
              <Button size="lg" variant="outline" asChild className="text-lg px-12 py-6 rounded-lg font-semibold border-2 hover:bg-muted/50 transition-all duration-300">
                <Link href="/jobs">Explore Opportunities</Link>
              </Button>
            </div>

            {/* Social proof */}
            <div className="mt-16 text-center">
              <p className="text-muted-foreground mb-4">Trusted by students and professionals at</p>
              <div className="flex flex-wrap justify-center items-center gap-8 text-sm font-semibold text-muted-foreground">
                <span>🏢 Google</span>
                <span>🚀 Startup Inc</span>
                <span>💼 Microsoft</span>
                <span>⚡ TechCorp</span>
                <span>🎯 InnovateLab</span>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
