import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { dashboardApi, jobsApi, usersApi, applicationsApi, blogsApi, authApi, uploadApi } from '../services/api'
import toast from 'react-hot-toast'

// Query Keys
export const queryKeys = {
  dashboard: ['dashboard'] as const,
  dashboardStats: () => [...queryKeys.dashboard, 'stats'] as const,

  jobs: ['jobs'] as const,
  jobsList: (params: Record<string, any>) => [...queryKeys.jobs, 'list', params] as const,
  job: (id: string) => [...queryKeys.jobs, 'detail', id] as const,
  jobCategories: () => [...queryKeys.jobs, 'categories'] as const,

  users: ['users'] as const,
  usersList: (params: Record<string, any>) => [...queryKeys.users, 'list', params] as const,
  user: (id: string) => [...queryKeys.users, 'detail', id] as const,

  applications: ['applications'] as const,
  applicationsList: (params: Record<string, any>) => [...queryKeys.applications, 'list', params] as const,

  blogs: ['blogs'] as const,
  blogsList: (params: Record<string, any>) => [...queryKeys.blogs, 'list', params] as const,
  blog: (id: string) => [...queryKeys.blogs, 'detail', id] as const,
}

// Dashboard Hooks
export const useDashboardStats = () => {
  return useQuery({
    queryKey: queryKeys.dashboardStats(),
    queryFn: dashboardApi.getStats,
  })
}

// Jobs Hooks
export const useJobs = (params: Record<string, any> = {}) => {
  return useQuery({
    queryKey: queryKeys.jobsList(params),
    queryFn: () => jobsApi.getJobs(params),
  })
}

export const useJob = (id: string) => {
  return useQuery({
    queryKey: queryKeys.job(id),
    queryFn: () => jobsApi.getJob(id),
    enabled: !!id,
  })
}

export const useCreateJob = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: jobsApi.createJob,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.jobs })
      queryClient.invalidateQueries({ queryKey: queryKeys.dashboardStats() })
      toast.success('Job created successfully')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to create job')
    },
  })
}

export const useUpdateJob = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) => jobsApi.updateJob(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.jobs })
      queryClient.invalidateQueries({ queryKey: queryKeys.job(id) })
      queryClient.invalidateQueries({ queryKey: queryKeys.dashboardStats() })
      toast.success('Job updated successfully')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update job')
    },
  })
}

export const useDeleteJob = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: jobsApi.deleteJob,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.jobs })
      queryClient.invalidateQueries({ queryKey: queryKeys.dashboardStats() })
      toast.success('Job deleted successfully')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to delete job')
    },
  })
}

export const useJobCategories = () => {
  return useQuery({
    queryKey: queryKeys.jobCategories(),
    queryFn: jobsApi.getCategories,
  })
}

// Users Hooks
export const useUsers = (params: Record<string, any> = {}) => {
  return useQuery({
    queryKey: queryKeys.usersList(params),
    queryFn: () => usersApi.getUsers(params),
  })
}

export const useUser = (id: string) => {
  return useQuery({
    queryKey: queryKeys.user(id),
    queryFn: () => usersApi.getUser(id),
    enabled: !!id,
  })
}

export const useDeleteUser = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: usersApi.deleteUser,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.users })
      queryClient.invalidateQueries({ queryKey: queryKeys.dashboardStats() })
      toast.success('User deleted successfully')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to delete user')
    },
  })
}

// Applications Hooks
export const useApplications = (params: Record<string, any> = {}) => {
  return useQuery({
    queryKey: queryKeys.applicationsList(params),
    queryFn: () => applicationsApi.getApplications(params),
  })
}

export const useUpdateApplicationStatus = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, status }: { id: string; status: string }) =>
      applicationsApi.updateApplicationStatus(id, status),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.applications })
      queryClient.invalidateQueries({ queryKey: queryKeys.dashboardStats() })
      toast.success('Application status updated successfully')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update application status')
    },
  })
}

// Blogs Hooks
export const useBlogs = (params: Record<string, any> = {}) => {
  return useQuery({
    queryKey: queryKeys.blogsList(params),
    queryFn: () => blogsApi.getBlogs(params),
  })
}

export const useBlog = (id: string) => {
  return useQuery({
    queryKey: queryKeys.blog(id),
    queryFn: () => blogsApi.getBlog(id),
    enabled: !!id,
  })
}

export const useCreateBlog = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: blogsApi.createBlog,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.blogs })
      queryClient.invalidateQueries({ queryKey: queryKeys.dashboardStats() })
      toast.success('Blog created successfully')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to create blog')
    },
  })
}

export const useUpdateBlog = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) => blogsApi.updateBlog(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.blogs })
      queryClient.invalidateQueries({ queryKey: queryKeys.blog(id) })
      queryClient.invalidateQueries({ queryKey: queryKeys.dashboardStats() })
      toast.success('Blog updated successfully')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update blog')
    },
  })
}

export const useDeleteBlog = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: blogsApi.deleteBlog,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.blogs })
      queryClient.invalidateQueries({ queryKey: queryKeys.dashboardStats() })
      toast.success('Blog deleted successfully')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to delete blog')
    },
  })
}

// Auth Hooks
export const useRegister = () => {
  return useMutation({
    mutationFn: authApi.register,
    onSuccess: () => {
      toast.success('Registration successful')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Registration failed')
    },
  })
}

export const useLogin = () => {
  return useMutation({
    mutationFn: authApi.login,
    onSuccess: () => {
      toast.success('Login successful')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Login failed')
    },
  })
}

// Upload Hooks
export const useUploadBlogImage = () => {
  return useMutation({
    mutationFn: uploadApi.uploadBlogImage,
    onSuccess: () => {
      toast.success('Image uploaded successfully')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to upload image')
    },
  })
}

export const useUploadCompanyLogo = () => {
  return useMutation({
    mutationFn: uploadApi.uploadCompanyLogo,
    onSuccess: () => {
      toast.success('Logo uploaded successfully')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to upload logo')
    },
  })
}
