'use client'

import { useState } from 'react'
import Link from 'next/link'
import { useQuery } from '@tanstack/react-query'
import { useRequireAuth } from '@/hooks/use-auth'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import {
  Search,
  Calendar,
  Building,
  MapPin,
  Clock,
  FileText,
  ExternalLink,
  Loader2,
  Briefcase
} from 'lucide-react'
import { api, JobApplication } from '@/lib/api'
import { ApplicationCardSkeleton } from '@/components/loading'

export default function ApplicationsPage() {
  const { user, loading } = useRequireAuth()
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')

  const { data: applicationsData, isLoading, error } = useQuery({
    queryKey: ['my-applications'],
    queryFn: () => api.getMyApplications(),
    enabled: !!user,
  })

  const applications = applicationsData?.applications || []

  // Filter applications based on search and status
  const filteredApplications = applications?.filter(application => {
    const matchesSearch = !searchQuery ||
      application.job.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      application.job.companyName.toLowerCase().includes(searchQuery.toLowerCase())

    const matchesStatus = !statusFilter || statusFilter === 'all' || application.status === statusFilter

    return matchesSearch && matchesStatus
  }) || []

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'REVIEWED':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'SHORTLISTED':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'ACCEPTED':
        return 'bg-emerald-100 text-emerald-800 border-emerald-200'
      case 'REJECTED':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'HIRED':
        return 'bg-purple-100 text-purple-800 border-purple-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'Under Review'
      case 'REVIEWED':
        return 'Reviewed'
      case 'SHORTLISTED':
        return 'Shortlisted'
      case 'ACCEPTED':
        return 'Accepted'
      case 'REJECTED':
        return 'Not Selected'
      case 'HIRED':
        return 'Hired'
      default:
        return status
    }
  }

  const applicationStats = applications ? {
    total: applications.length,
    pending: applications.filter(app => app.status === 'PENDING').length,
    shortlisted: applications.filter(app => app.status === 'SHORTLISTED').length,
    hired: applications.filter(app => app.status === 'HIRED').length,
  } : { total: 0, pending: 0, shortlisted: 0, hired: 0 }

  if (loading || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-foreground mb-2">My Applications</h1>
            <p className="text-muted-foreground">
              Track the status of your job applications and manage your career journey.
            </p>
          </div>

          {/* Stats Cards */}
          <div className="grid gap-6 md:grid-cols-4 mb-8">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Total Applications</p>
                    <p className="text-2xl font-bold text-foreground">{applicationStats.total}</p>
                  </div>
                  <Briefcase className="h-8 w-8 text-primary" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Under Review</p>
                    <p className="text-2xl font-bold text-foreground">{applicationStats.pending}</p>
                  </div>
                  <Clock className="h-8 w-8 text-yellow-500" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Shortlisted</p>
                    <p className="text-2xl font-bold text-foreground">{applicationStats.shortlisted}</p>
                  </div>
                  <FileText className="h-8 w-8 text-green-500" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Hired</p>
                    <p className="text-2xl font-bold text-foreground">{applicationStats.hired}</p>
                  </div>
                  <Badge className="h-8 w-8 rounded-full p-0 flex items-center justify-center bg-emerald-500">
                    ✓
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Filters */}
          <Card className="mb-8">
            <CardContent className="p-6">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                    <Input
                      placeholder="Search applications..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <div className="md:w-48">
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="Filter by status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Statuses</SelectItem>
                      <SelectItem value="PENDING">Under Review</SelectItem>
                      <SelectItem value="REVIEWED">Reviewed</SelectItem>
                      <SelectItem value="SHORTLISTED">Shortlisted</SelectItem>
                      <SelectItem value="ACCEPTED">Accepted</SelectItem>
                      <SelectItem value="REJECTED">Not Selected</SelectItem>
                      <SelectItem value="HIRED">Hired</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Applications List */}
          {isLoading ? (
            <div className="space-y-6">
              {Array.from({ length: 3 }).map((_, i) => (
                <ApplicationCardSkeleton key={i} />
              ))}
            </div>
          ) : error ? (
            <Card>
              <CardContent className="p-12 text-center">
                <p className="text-muted-foreground text-lg mb-4">Failed to load applications.</p>
                <Button onClick={() => window.location.reload()}>Try Again</Button>
              </CardContent>
            </Card>
          ) : !filteredApplications.length ? (
            <Card>
              <CardContent className="p-12 text-center">
                {applications?.length === 0 ? (
                  <>
                    <Briefcase className="h-16 w-16 text-muted-foreground mx-auto mb-6" />
                    <h3 className="text-xl font-semibold text-foreground mb-2">No Applications Yet</h3>
                    <p className="text-muted-foreground mb-6">
                      You haven&apos;t applied to any jobs yet. Start exploring opportunities!
                    </p>
                    <Button asChild>
                      <Link href="/jobs">Browse Jobs</Link>
                    </Button>
                  </>
                ) : (
                  <>
                    <Search className="h-16 w-16 text-muted-foreground mx-auto mb-6" />
                    <h3 className="text-xl font-semibold text-foreground mb-2">No Results Found</h3>
                    <p className="text-muted-foreground mb-6">
                      No applications match your current filters.
                    </p>
                    <Button variant="outline" onClick={() => { setSearchQuery(''); setStatusFilter('all') }}>
                      Clear Filters
                    </Button>
                  </>
                )}
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-6">
              {filteredApplications.map((application: JobApplication) => (
                <Card key={application.id} className="hover:shadow-lg transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                      <div className="flex-1">
                        <div className="flex items-start justify-between mb-4">
                          <div>
                            <h3 className="text-xl font-semibold text-foreground mb-2">
                              {application.job.title}
                            </h3>
                            <div className="flex items-center text-muted-foreground mb-2">
                              <Building className="h-4 w-4 mr-2" />
                              <span>{application.job.companyName}</span>
                            </div>
                            <div className="flex items-center text-muted-foreground mb-4">
                              <MapPin className="h-4 w-4 mr-2" />
                              <span>{application.job.location}</span>
                              <span className="mx-2">•</span>
                              <span>{application.job.workLocationType}</span>
                            </div>
                          </div>
                          <Badge className={`${getStatusColor(application.status)} border`}>
                            {getStatusLabel(application.status)}
                          </Badge>
                        </div>

                        <div className="flex items-center text-sm text-muted-foreground mb-4">
                          <Calendar className="h-4 w-4 mr-2" />
                          <span>Applied on {new Date(application.createdAt).toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric'
                          })}</span>
                        </div>

                        {(application.message || application.coverLetter) && (
                          <div className="bg-muted/50 rounded-lg p-4 mb-4">
                            <p className="text-sm font-medium text-foreground mb-2">Cover Letter:</p>
                            <p className="text-sm text-muted-foreground line-clamp-3">
                              {application.message || application.coverLetter}
                            </p>
                          </div>
                        )}
                      </div>

                      <div className="flex flex-col sm:flex-row lg:flex-col gap-2 lg:w-48">
                        <Button asChild variant="outline" className="flex-1">
                          <Link href={`/jobs/${application.job.slug}`}>
                            <ExternalLink className="h-4 w-4 mr-2" />
                            View Job
                          </Link>
                        </Button>
                        {application.status === 'SHORTLISTED' && (
                          <Badge variant="secondary" className="text-center py-2">
                            🎉 Congratulations!
                          </Badge>
                        )}
                        {application.status === 'HIRED' && (
                          <Badge className="text-center py-2 bg-emerald-500">
                            🎊 You got the job!
                          </Badge>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {/* Call to Action */}
          {applications && applications.length > 0 && (
            <Card className="mt-12">
              <CardContent className="p-8 text-center">
                <h3 className="text-xl font-semibold text-foreground mb-2">
                  Keep Exploring Opportunities
                </h3>
                <p className="text-muted-foreground mb-6">
                  Don&apos;t stop here! Continue browsing jobs and applying to increase your chances of landing your dream role.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild>
                    <Link href="/jobs">Browse More Jobs</Link>
                  </Button>
                  <Button variant="outline" asChild>
                    <Link href="/dashboard/profile">Update Profile</Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}
