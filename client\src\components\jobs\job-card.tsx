import Link from 'next/link'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Building, MapPin, Clock, DollarSign } from 'lucide-react'
import { Job, formatSalary, getJobTypeLabel, getExperienceLabel, getWorkTypeLabel, formatDate } from '@/lib/api'

interface JobCardProps {
  job: Job
}

export function JobCard({ job }: JobCardProps) {
  return (
    <Card className="glass border-0 hover:shadow-lg transition-all duration-300 h-full flex flex-col group overflow-hidden">
      <CardHeader className="pb-4 relative">
        {job.isFeatured && (
          <div className="absolute -top-2 -right-2 w-16 h-16">
            <div className="absolute inset-0 bg-gradient-to-br from-accent to-primary rounded-full"></div>
            <div className="absolute inset-1 bg-background rounded-full flex items-center justify-center">
              <span className="text-xs font-bold text-accent">⭐</span>
            </div>
          </div>
        )}

        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-xl font-bold mb-3 line-clamp-2 group-hover:text-primary transition-colors">
              {job.title}
            </CardTitle>
            <div className="flex items-center text-muted-foreground mb-2">
              <div className="w-8 h-8 rounded-xl bg-primary/10 flex items-center justify-center mr-3">
                <Building className="h-4 w-4 text-primary" />
              </div>
              <span className="font-semibold">{job.companyName}</span>
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="flex-1 flex flex-col p-6">
        <div className="space-y-4 flex-1">
          {/* Location and Work Type */}
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2 px-3 py-1.5 bg-muted/50 rounded-full">
              <MapPin className="h-3 w-3 text-primary" />
              <span className="text-xs font-semibold">{job.location}</span>
            </div>
            <div className="flex items-center gap-2 px-3 py-1.5 bg-accent/10 rounded-full">
              <span className="text-xs font-semibold text-accent">{getWorkTypeLabel(job.workLocationType || '')}</span>
            </div>
          </div>

          {/* Job Type and Experience */}
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2 px-3 py-1.5 bg-primary/10 rounded-full">
              <Clock className="h-3 w-3 text-primary" />
              <span className="text-xs font-semibold text-primary">{getJobTypeLabel(job.jobType)}</span>
            </div>
            <div className="flex items-center gap-2 px-3 py-1.5 bg-secondary/50 rounded-full">
              <span className="text-xs font-semibold">{getExperienceLabel(job.experienceLevel)}</span>
            </div>
          </div>

          {/* Salary */}
          {(job.salaryMin || job.salaryMax || job.salaryNegotiable) && (
            <div className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-2xl border border-green-500/20">
              <DollarSign className="h-4 w-4 text-green-600" />
              <span className="font-bold text-green-700">{formatSalary(job.salaryMin, job.salaryMax, job.salaryNegotiable)}</span>
            </div>
          )}

          {/* Description Preview */}
          {job.description && (
            <p className="text-sm text-muted-foreground line-clamp-3 leading-relaxed">
              {job.description.replace(/<[^>]*>/g, '').substring(0, 120)}...
            </p>
          )}

          {/* Category and Posted Date */}
          <div className="flex flex-wrap gap-2 pt-2">
            <Badge variant="outline" className="rounded-full font-semibold">{job.category}</Badge>
            <Badge variant="outline" className="text-xs rounded-full">
              {formatDate(job.createdAt)}
            </Badge>
          </div>
        </div>

        {/* Action Button */}
        <div className="mt-6">
          <Button asChild className="w-full rounded-lg font-semibold h-12 gradient-primary hover:shadow-lg transition-all duration-200">
            <Link href={`/jobs/${job.slug}`}>
              View Details →
            </Link>
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
