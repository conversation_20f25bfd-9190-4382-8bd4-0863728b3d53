export default function FontTestPage() {
  return (
    <div className="container mx-auto px-4 py-8 space-y-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="mb-8">Font Implementation Test</h1>
        
        <div className="space-y-12">
          {/* Headings Section */}
          <section className="space-y-4">
            <h2 className="text-2xl font-semibold mb-4">Headings (Urbanist Font)</h2>
            <div className="space-y-4">
              <h1>This is an H1 heading using Urbanist font with text-3xl</h1>
              <h1 className="text-4xl">This is a larger H1 with text-4xl</h1>
              <h1 className="text-2xl">This is a smaller H1 with text-2xl</h1>
            </div>
          </section>

          {/* Paragraphs Section */}
          <section className="space-y-4">
            <h2 className="text-2xl font-semibold mb-4">Paragraphs (Inter Font)</h2>
            <div className="space-y-4">
              <p>
                This is a paragraph using Inter font with text-base. Lorem ipsum dolor sit amet, 
                consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore 
                magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.
              </p>
              <p className="text-lg">
                This is a larger paragraph with text-lg. Duis aute irure dolor in reprehenderit 
                in voluptate velit esse cillum dolore eu fugiat nulla pariatur.
              </p>
              <p className="text-sm">
                This is a smaller paragraph with text-sm. Excepteur sint occaecat cupidatat non 
                proident, sunt in culpa qui officia deserunt mollit anim id est laborum.
              </p>
            </div>
          </section>

          {/* Code and Labels Section */}
          <section className="space-y-4">
            <h2 className="text-2xl font-semibold mb-4">Code & Labels (JetBrains Mono Font)</h2>
            <div className="space-y-4">
              <div>
                <label htmlFor="test-input" className="block mb-2">
                  This is a label using JetBrains Mono with text-xs
                </label>
                <input 
                  id="test-input" 
                  type="text" 
                  className="border rounded px-3 py-2" 
                  placeholder="Input field"
                />
              </div>
              
              <div>
                <p className="mb-2">Inline code example:</p>
                <p>Here is some <code className="bg-gray-100 px-2 py-1 rounded">inline code</code> in a sentence.</p>
              </div>
              
              <div>
                <p className="mb-2">Code block example:</p>
                <pre className="bg-gray-100 p-4 rounded overflow-x-auto">
                  <code>{`function example() {
  console.log("This is JetBrains Mono font");
  return "Hello World";
}`}</code>
                </pre>
              </div>
            </div>
          </section>

          {/* Manual Font Classes Section */}
          <section className="space-y-4">
            <h2 className="text-2xl font-semibold mb-4">Manual Font Classes</h2>
            <div className="space-y-4">
              <div>
                <p className="mb-2">Using font-heading class manually:</p>
                <p className="font-heading text-2xl">This text uses font-heading (Urbanist)</p>
              </div>
              
              <div>
                <p className="mb-2">Using font-sans class manually:</p>
                <p className="font-sans text-lg">This text uses font-sans (Inter)</p>
              </div>
              
              <div>
                <p className="mb-2">Using font-mono class manually:</p>
                <p className="font-mono text-base">This text uses font-mono (JetBrains Mono)</p>
              </div>
            </div>
          </section>

          {/* Mixed Content Example */}
          <section className="space-y-4">
            <h2 className="text-2xl font-semibold mb-4">Mixed Content Example</h2>
            <div className="bg-gray-50 p-6 rounded-lg space-y-4">
              <h1>Welcome to Our Job Portal</h1>
              <p>
                Find your dream job with our advanced search features. We connect talented 
                professionals with amazing opportunities across various industries.
              </p>
              <div className="space-y-2">
                <label htmlFor="job-search">Search for jobs:</label>
                <input 
                  id="job-search" 
                  type="text" 
                  className="w-full border rounded px-3 py-2" 
                  placeholder="e.g. Software Engineer"
                />
              </div>
              <p>
                Use our <code>advanced filters</code> to narrow down your search results 
                and find the perfect match for your skills and preferences.
              </p>
            </div>
          </section>
        </div>
      </div>
    </div>
  );
}
