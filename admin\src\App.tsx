import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { QueryClientProvider } from '@tanstack/react-query'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
import { Toaster } from 'react-hot-toast'
import { queryClient } from './lib/queryClient'
import { useAuthStore } from './lib/store'
import { useEffect } from 'react'
import { authApi } from './services/api'

// Pages
import LoginPage from './pages/LoginPage'
import RegisterPage from './pages/RegisterPage'
import DashboardPage from './pages/DashboardPage'
import JobsPage from './pages/JobsPage'
import JobFormPage from './pages/JobFormPage'
import JobViewPage from './pages/JobViewPage'
import JobApplicantsPage from './pages/JobApplicantsPage'
import UsersPage from './pages/UsersPage'
import ApplicationsPage from './pages/ApplicationsPage'
import BlogsPage from './pages/BlogsPage'
import BlogFormPage from './pages/BlogFormPage'
import BlogViewPage from './pages/BlogViewPage'
import AnalyticsPage from './pages/AnalyticsPage'

// Layout
import Layout from './components/Layout'

function App() {
  const { isAuthenticated, admin, setLoading, login, logout } = useAuthStore()

  useEffect(() => {
    const checkAuth = async () => {
      const token = localStorage.getItem('admin_token')
      if (token && !admin) {
        try {
          setLoading(true)
          const response = await authApi.getProfile()
          login(response.admin, token)
        } catch (error) {
          logout()
        } finally {
          setLoading(false)
        }
      }
    }

    checkAuth()
  }, [admin, login, logout, setLoading])

  return (
    <QueryClientProvider client={queryClient}>
      <Router>
        <div className="min-h-screen bg-gray-50">
          <Routes>
            <Route
              path="/login"
              element={
                isAuthenticated ? <Navigate to="/" replace /> : <LoginPage />
              }
            />
            <Route
              path="/register"
              element={
                isAuthenticated ? <Navigate to="/" replace /> : <RegisterPage />
              }
            />
            <Route
              path="/*"
              element={
                isAuthenticated ? (
                  <Layout>
                    <Routes>
                      <Route path="/" element={<DashboardPage />} />
                      <Route path="/jobs" element={<JobsPage />} />
                      <Route path="/jobs/new" element={<JobFormPage />} />
                      <Route path="/jobs/:id" element={<JobViewPage />} />
                      <Route path="/jobs/:id/applicants" element={<JobApplicantsPage />} />
                      <Route path="/jobs/:id/edit" element={<JobFormPage />} />
                      <Route path="/users" element={<UsersPage />} />
                      <Route path="/applications" element={<ApplicationsPage />} />
                      <Route path="/blogs" element={<BlogsPage />} />
                      <Route path="/blogs/new" element={<BlogFormPage />} />
                      <Route path="/blogs/:id" element={<BlogViewPage />} />
                      <Route path="/blogs/:id/edit" element={<BlogFormPage />} />
                      <Route path="/analytics" element={<AnalyticsPage />} />
                    </Routes>
                  </Layout>
                ) : (
                  <Navigate to="/login" replace />
                )
              }
            />
          </Routes>
          <Toaster position="top-right" />
        </div>
      </Router>
      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  )
}

export default App
