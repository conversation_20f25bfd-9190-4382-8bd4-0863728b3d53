'use client'

import { useState, useEffect, useRef, Suspense } from 'react'
import { useSearchParams } from 'next/navigation'
import { useRequireAuth } from '@/hooks/use-auth'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'

import {
  Mail,
  Phone,
  MapPin,
  Upload,
  FileText,
  Loader2,
  CheckCircle,
  AlertCircle
} from 'lucide-react'
import { api, UserProfile, getExperienceLabel } from '@/lib/api'
import { toast } from 'sonner'

function ProfilePageContent() {
  const { user, loading, updateUserProfile } = useRequireAuth()
  const searchParams = useSearchParams()
  const isWelcome = searchParams.get('welcome') === 'true'
  const queryClient = useQueryClient()



  const [formData, setFormData] = useState<Partial<UserProfile>>({
    firstName: '',
    lastName: '',
    phone: '',
    bio: '',
    skills: [],
    experience: '',
    education: '',
    location: '',
    website: '',
    linkedin: '',
    github: '',
  })
  const [skillInput, setSkillInput] = useState('')
  const [errors, setErrors] = useState<Record<string, string>>({})

  const profilePicRef = useRef<HTMLInputElement>(null)
  const resumeRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    if (user) {
      setFormData({
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        phone: user.phone || '',
        bio: user.bio || '',
        skills: user.skills || [],
        experience: user.experience || '',
        education: user.education || '',
        location: user.location || '',
        website: user.website || '',
        linkedin: user.linkedin || '',
        github: user.github || '',
      })
    }
  }, [user])

  const updateProfileMutation = useMutation({
    mutationFn: (data: Partial<UserProfile>) => api.updateProfile(data),
    onSuccess: (response) => {
      toast.success(response.message || 'Profile updated successfully!')
      // Update the user in Zustand store instead of making API call
      updateUserProfile(response.user)
      queryClient.invalidateQueries({ queryKey: ['profile'] })
    },
    onError: (error: any) => {
      toast.error(error?.message || 'Failed to update profile')
    }
  })

  const uploadFileMutation = useMutation({
    mutationFn: ({ file, type }: { file: File; type: 'resume' | 'profile-picture' }) =>
      api.uploadFile(file, type),
    onSuccess: (data, variables) => {
      // Preserve current form data and only update the specific file field
      const updateData = {
        ...formData,
        website: formData.website?.trim() || undefined,
        linkedin: formData.linkedin?.trim() || undefined,
        github: formData.github?.trim() || undefined,
        phone: formData.phone?.trim() || undefined,
      }

      if (variables.type === 'resume') {
        updateProfileMutation.mutate({ ...updateData, resume: data.url })
      } else {
        updateProfileMutation.mutate({ ...updateData, profilePicture: data.url })
      }
    },
    onError: (error: any) => {
      toast.error(error?.message || 'Failed to upload file')
    }
  })

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleAddSkill = () => {
    const skill = skillInput.trim()
    if (skill && !(formData.skills || []).includes(skill)) {
      setFormData(prev => ({
        ...prev,
        skills: [...(prev.skills || []), skill]
      }))
      setSkillInput('')
    }
  }

  const handleRemoveSkill = (skillToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      skills: prev.skills?.filter(s => s !== skillToRemove) || []
    }))
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.firstName?.trim()) newErrors.firstName = 'First name is required'
    if (!formData.lastName?.trim()) newErrors.lastName = 'Last name is required'
    if (formData.bio && formData.bio.length > 500) newErrors.bio = 'Bio must be under 500 chars'
    if (formData.location && formData.location.length > 100) newErrors.location = 'Location must be under 100 chars'

    // Phone validation (optional)
    if (formData.phone && formData.phone.trim() && !/^\+?[\d\s\-\(\)]+$/.test(formData.phone)) {
      newErrors.phone = 'Please enter a valid phone number'
    }

    // URL validation for social links (optional)
    const urlRegex = /^https?:\/\/.+/
    if (formData.website && formData.website.trim() && !urlRegex.test(formData.website)) {
      newErrors.website = 'Please enter a valid URL (starting with http:// or https://)'
    }
    if (formData.linkedin && formData.linkedin.trim() && !urlRegex.test(formData.linkedin)) {
      newErrors.linkedin = 'Please enter a valid LinkedIn URL'
    }
    if (formData.github && formData.github.trim() && !urlRegex.test(formData.github)) {
      newErrors.github = 'Please enter a valid GitHub URL'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (validateForm()) {
      // Filter out empty social links before sending to backend
      const cleanedData = {
        ...formData,
        website: formData.website?.trim() || undefined,
        linkedin: formData.linkedin?.trim() || undefined,
        github: formData.github?.trim() || undefined,
        phone: formData.phone?.trim() || undefined,
      }
      updateProfileMutation.mutate(cleanedData)
    }
  }

  const triggerFileUpload = (type: 'resume' | 'profile-picture') => {
    if (type === 'resume') resumeRef.current?.click()
    else profilePicRef.current?.click()
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>, type: 'resume' | 'profile-picture') => {
    const file = e.target.files?.[0]
    if (file) uploadFileMutation.mutate({ file, type })
  }

  if (loading || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  const completionItems = [
    ['firstName', 'First Name', !!formData.firstName],
    ['lastName', 'Last Name', !!formData.lastName],
    ['bio', 'Bio', !!formData.bio],
    ['skills', 'Skills', (formData.skills || []).length > 0],
    ['location', 'Location', !!formData.location],
    ['resume', 'Resume', !!user.resume]
  ]
  const completedCount = completionItems.filter(([, , done]) => done).length
  const completionPct = Math.round((completedCount / completionItems.length) * 100)

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-muted/10 to-secondary/5">
      {/* Background Elements */}
      <div className="absolute top-20 left-10 w-72 h-72 bg-primary/10 rounded-full blur-3xl float"></div>
      <div className="absolute bottom-20 right-10 w-96 h-96 bg-accent/10 rounded-full blur-3xl float" style={{animationDelay: '2s'}}></div>

      <div className="container mx-auto p-4 lg:px-8 py-8 relative">
        <div className="max-w-6xl mx-auto space-y-12">
          {/* Hero Header */}
          <div className="text-center space-y-4">
            <h1 className="text-4xl md:text-6xl font-black text-foreground">
              Your Profile{' '}
              <span className="gradient-primary bg-clip-text text-transparent">
                Matters
              </span>
            </h1>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Make a killer first impression. Complete your profile and let opportunities find you.
            </p>
          </div>

          {isWelcome && (
            <Card className="glass border-0 rounded-lg overflow-hidden max-w-3xl mx-auto">
              <CardContent className="p-8">
                <div className="flex items-start gap-6">
                  <div className="w-16 h-16 rounded-lg bg-gradient-to-br from-primary to-accent flex items-center justify-center flex-shrink-0">
                    <CheckCircle className="h-8 w-8 text-white" />
                  </div>
                  <div className="flex-1">
                    <h2 className="text-2xl font-bold text-foreground mb-3">Welcome to JobPortal! 🎉</h2>
                    <p className="text-muted-foreground mb-6 leading-relaxed">
                      You're almost there! Complete your profile to unlock the full potential of our platform and connect with top employers.
                    </p>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-semibold text-foreground">Profile Completion</span>
                        <span className="text-sm font-bold text-primary">{completionPct}% complete</span>
                      </div>
                      <div className="relative">
                        <div className="flex-1 bg-muted/50 rounded-full h-3">
                          <div
                            className="bg-gradient-to-r from-primary to-accent h-3 rounded-full transition-all duration-500 ease-out"
                            style={{ width: `${completionPct}%` }}
                          />
                        </div>
                        {completionPct >= 100 && (
                          <div className="absolute -top-1 -right-1 w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                            <CheckCircle className="h-3 w-3 text-white" />
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          <div className="grid lg:grid-cols-3 gap-8">
            {/* Sidebar Cards */}
            <div className="space-y-8">
              <Card className="glass border-0 rounded-3xl overflow-hidden">
                <CardContent className="p-8 text-center">
                  <div className="relative inline-block mb-6">
                    <div className="relative">
                      <Avatar className="w-32 h-32 ring-4 ring-primary/20">
                        <AvatarImage src={user.profilePicture} alt={user.email || 'User'} />
                        <AvatarFallback className="text-3xl font-bold bg-gradient-to-br from-primary to-accent text-white">
                          {user.email?.[0]?.toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <Button
                        size="sm"
                        className="absolute -bottom-2 -right-2 rounded-full w-10 h-10 p-0 gradient-primary hover:shadow-lg transition-all duration-200"
                        onClick={() => triggerFileUpload('profile-picture')}
                        disabled={uploadFileMutation.isPending}
                      >
                        {uploadFileMutation.isPending ?
                          <Loader2 className="h-4 w-4 animate-spin" /> :
                          <Upload className="h-4 w-4" />
                        }
                      </Button>
                    </div>
                    <input ref={profilePicRef} type="file" accept="image/*" className="hidden" onChange={e => handleFileChange(e, 'profile-picture')} />
                  </div>

                  <h2 className="text-2xl font-bold text-foreground mb-4">
                    {formData.firstName || formData.lastName ?
                      `${formData.firstName} ${formData.lastName}` :
                      'Complete Your Profile'
                    }
                  </h2>

                  <div className="space-y-3 mb-6">
                    <div className="flex items-center justify-center gap-3 px-4 py-2 bg-muted/50 rounded-lg">
                      <Mail className="h-4 w-4 text-primary" />
                      <span className="text-sm font-medium">{user.email}</span>
                    </div>
                    {user.phone && (
                      <div className="flex items-center justify-center gap-3 px-4 py-2 bg-muted/50 rounded-lg">
                        <Phone className="h-4 w-4 text-accent" />
                        <span className="text-sm font-medium">{user.phone}</span>
                      </div>
                    )}
                    {formData.location && (
                      <div className="flex items-center justify-center gap-3 px-4 py-2 bg-muted/50 rounded-lg">
                        <MapPin className="h-4 w-4 text-destructive" />
                        <span className="text-sm font-medium">{formData.location}</span>
                      </div>
                    )}
                  </div>

                  {user.experienceLevel && (
                    <Badge className="rounded-full px-4 py-2 font-semibold gradient-primary text-primary-foreground">
                      🎓 {getExperienceLabel(user.experienceLevel)}
                    </Badge>
                  )}
                </CardContent>
              </Card>

              <Card className="glass border-0 rounded-lg overflow-hidden">
                <CardHeader className="bg-gradient-to-r from-primary/5 to-accent/5 border-b border-border/50">
                  <CardTitle className="flex items-center gap-3 text-xl font-bold">
                    <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-primary to-accent flex items-center justify-center">
                      <CheckCircle className="h-5 w-5 text-white" />
                    </div>
                    Profile Completion
                  </CardTitle>
                  <CardDescription className="text-base font-semibold">
                    {completionPct}% complete {completionPct >= 100 ? '🎉' : '📈'}
                  </CardDescription>
                </CardHeader>
                <CardContent className="p-6">
                  <div className="space-y-4">
                    {completionItems.map(([key, label, done]) => (
                      <div key={String(key)} className="flex items-center gap-3 p-3 rounded-lg bg-muted/30 hover:bg-muted/50 transition-colors">
                        <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
                          done ? 'bg-green-500' : 'bg-muted-foreground/20'
                        }`}>
                          {done ?
                            <CheckCircle className="h-4 w-4 text-white" /> :
                            <AlertCircle className="h-4 w-4 text-muted-foreground" />
                          }
                        </div>
                        <span className={`font-medium ${done ? 'text-foreground' : 'text-muted-foreground'}`}>
                          {label}
                        </span>
                        {done && <span className="ml-auto text-green-500 font-semibold">✓</span>}
                      </div>
                    ))}
                  </div>

                  {completionPct >= 100 && (
                    <div className="mt-6 p-4 bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-lg border border-green-500/20">
                      <p className="text-green-700 font-semibold text-center">
                        🎉 Profile Complete! You're ready to apply for jobs!
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card className="glass border-0 rounded-3xl overflow-hidden">
                <CardHeader className="bg-gradient-to-r from-destructive/5 to-orange-500/5 border-b border-border/50">
                  <CardTitle className="flex items-center gap-3 text-xl font-black">
                    <div className="w-8 h-8 rounded-2xl bg-gradient-to-br from-destructive to-orange-500 flex items-center justify-center">
                      <FileText className="h-5 w-5 text-white" />
                    </div>
                    Resume
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-6">
                  {user.resume ? (
                    <div className="space-y-4">
                      <div className="flex items-center gap-3 p-4 bg-green-500/10 rounded-2xl border border-green-500/20">
                        <CheckCircle className="h-5 w-5 text-green-600" />
                        <span className="font-bold text-green-700">Resume uploaded! 🎉</span>
                      </div>
                      <div className="flex gap-3">
                        <Button variant="outline" size="sm" asChild className="flex-1 rounded-2xl font-semibold">
                          <a href={user.resume} target="_blank" rel="noopener noreferrer">
                            👁️ View Resume
                          </a>
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => triggerFileUpload('resume')}
                          disabled={uploadFileMutation.isPending}
                          className="flex-1 rounded-2xl font-semibold"
                        >
                          {uploadFileMutation.isPending ?
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" /> :
                            <Upload className="mr-2 h-4 w-4" />
                          }
                          Update
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <div className="p-4 bg-muted/50 rounded-2xl text-center">
                        <p className="text-muted-foreground font-medium mb-2">📄 No resume uploaded</p>
                        <p className="text-xs text-muted-foreground">Upload your resume to complete your profile</p>
                      </div>
                      <Button
                        variant="outline"
                        className="w-full rounded-2xl font-bold h-12 hover:scale-105 transition-all duration-200"
                        onClick={() => triggerFileUpload('resume')}
                        disabled={uploadFileMutation.isPending}
                      >
                        {uploadFileMutation.isPending ?
                          <Loader2 className="mr-2 h-5 w-5 animate-spin" /> :
                          <Upload className="mr-2 h-5 w-5" />
                        }
                        Upload Resume
                      </Button>
                    </div>
                  )}
                  <input ref={resumeRef} type="file" accept=".pdf,.doc,.docx" className="hidden" onChange={e => handleFileChange(e, 'resume')} />
                </CardContent>
              </Card>

              {/* Social Links Display */}
              {(formData.website || formData.linkedin || formData.github) && (
                <Card className="glass border-0 rounded-3xl overflow-hidden">
                  <CardHeader className="bg-gradient-to-r from-accent/5 to-orange-500/5 border-b border-border/50">
                    <CardTitle className="flex items-center gap-3 text-xl font-black">
                      <div className="w-8 h-8 rounded-2xl bg-gradient-to-br from-accent to-orange-500 flex items-center justify-center">
                        <svg className="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                        </svg>
                      </div>
                      Social Links
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-6">
                    <div className="space-y-3">
                      {formData.website && (
                        <a href={formData.website} target="_blank" rel="noopener noreferrer"
                           className="flex items-center gap-3 p-3 rounded-2xl bg-muted/30 hover:bg-muted/50 transition-all duration-200 hover:scale-105">
                          <div className="w-8 h-8 rounded-xl bg-blue-500/10 flex items-center justify-center">
                            <svg className="h-4 w-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9" />
                            </svg>
                          </div>
                          <span className="font-semibold text-blue-600">Website</span>
                        </a>
                      )}
                      {formData.linkedin && (
                        <a href={formData.linkedin} target="_blank" rel="noopener noreferrer"
                           className="flex items-center gap-3 p-3 rounded-2xl bg-muted/30 hover:bg-muted/50 transition-all duration-200 hover:scale-105">
                          <div className="w-8 h-8 rounded-xl bg-blue-500/10 flex items-center justify-center">
                            <svg className="h-4 w-4 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                              <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                            </svg>
                          </div>
                          <span className="font-semibold text-blue-600">LinkedIn</span>
                        </a>
                      )}
                      {formData.github && (
                        <a href={formData.github} target="_blank" rel="noopener noreferrer"
                           className="flex items-center gap-3 p-3 rounded-2xl bg-muted/30 hover:bg-muted/50 transition-all duration-200 hover:scale-105">
                          <div className="w-8 h-8 rounded-xl bg-gray-500/10 flex items-center justify-center">
                            <svg className="h-4 w-4 text-gray-600" fill="currentColor" viewBox="0 0 24 24">
                              <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                            </svg>
                          </div>
                          <span className="font-semibold text-gray-600">GitHub</span>
                        </a>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>

            {/* Main Form */}
            <div className="lg:col-span-2">
              <Card className="glass border-0 rounded-3xl overflow-hidden">
                <CardHeader className="bg-gradient-to-r from-primary/5 to-accent/5 border-b border-border/50 p-8">
                  <CardTitle className="text-3xl font-black text-foreground">Profile Information ✨</CardTitle>
                  <CardDescription className="text-lg text-muted-foreground mt-2">
                    Update your profile to help employers find you and unlock amazing opportunities.
                  </CardDescription>
                </CardHeader>
                <CardContent className="p-8">
                  <form onSubmit={handleSubmit} className="space-y-8">
                    {/* Basic Info Section */}
                    <div className="space-y-6">
                      <h3 className="text-xl font-black text-foreground flex items-center gap-2">
                        👤 Basic Information
                      </h3>
                      <div className="grid md:grid-cols-2 gap-6">
                        <div>
                          <Label htmlFor="firstName" className="text-sm font-bold text-foreground mb-3 block">
                            First Name *
                          </Label>
                          <Input
                            id="firstName"
                            name="firstName"
                            value={formData.firstName}
                            onChange={handleInputChange}
                            placeholder="Your first name"
                            className={`rounded-2xl border-2 h-12 font-medium ${errors.firstName ? 'border-red-500' : ''}`}
                          />
                          {errors.firstName && <p className="text-red-600 text-sm mt-2 font-semibold">{errors.firstName}</p>}
                        </div>
                        <div>
                          <Label htmlFor="lastName" className="text-sm font-bold text-foreground mb-3 block">
                            Last Name *
                          </Label>
                          <Input
                            id="lastName"
                            name="lastName"
                            value={formData.lastName}
                            onChange={handleInputChange}
                            placeholder="Your last name"
                            className={`rounded-2xl border-2 h-12 font-medium ${errors.lastName ? 'border-red-500' : ''}`}
                          />
                          {errors.lastName && <p className="text-red-600 text-sm mt-2 font-semibold">{errors.lastName}</p>}
                        </div>
                      </div>

                      <div>
                        <Label htmlFor="phone" className="text-sm font-bold text-foreground mb-3 block">
                          📞 Phone Number (Optional)
                        </Label>
                        <Input
                          id="phone"
                          name="phone"
                          value={formData.phone}
                          onChange={handleInputChange}
                          placeholder="+****************"
                          className={`rounded-2xl border-2 h-12 font-medium ${errors.phone ? 'border-red-500' : ''}`}
                        />
                        {errors.phone && <p className="text-red-600 text-sm mt-2 font-semibold">{errors.phone}</p>}
                      </div>
                    </div>

                    {/* About Section */}
                    <div className="space-y-6">
                      <h3 className="text-xl font-black text-foreground flex items-center gap-2">
                        💬 About You
                      </h3>

                      <div>
                        <Label htmlFor="bio" className="text-sm font-bold text-foreground mb-3 block">
                          📝 Bio *
                        </Label>
                        <Textarea
                          id="bio"
                          name="bio"
                          value={formData.bio}
                          onChange={handleInputChange}
                          rows={4}
                          placeholder="Tell us about yourself, your passions, and what makes you unique..."
                          className={`rounded-2xl border-2 font-medium resize-none ${errors.bio ? 'border-red-500' : ''}`}
                        />
                        {errors.bio && <p className="text-red-600 text-sm mt-2 font-semibold">{errors.bio}</p>}
                        <p className="text-xs text-muted-foreground mt-2">
                          💡 Tip: Mention your career goals and what you're passionate about
                        </p>
                      </div>

                      <div>
                        <Label htmlFor="location" className="text-sm font-bold text-foreground mb-3 block">
                          📍 Location *
                        </Label>
                        <Input
                          id="location"
                          name="location"
                          value={formData.location}
                          onChange={handleInputChange}
                          placeholder="City, Country (e.g., San Francisco, CA)"
                          className={`rounded-2xl border-2 h-12 font-medium ${errors.location ? 'border-red-500' : ''}`}
                        />
                        {errors.location && <p className="text-red-600 text-sm mt-2 font-semibold">{errors.location}</p>}
                      </div>
                    </div>

                    {/* Skills Section */}
                    <div className="space-y-6">
                      <h3 className="text-xl font-black text-foreground flex items-center gap-2">
                        🚀 Skills *
                      </h3>

                      <div>
                        <Label htmlFor="skills" className="text-sm font-bold text-foreground mb-3 block">
                          Add Your Skills
                        </Label>
                        <div className="flex gap-3 mb-4">
                          <Input
                            value={skillInput}
                            onChange={e => setSkillInput(e.target.value)}
                            onKeyDown={e => e.key==='Enter'&& (e.preventDefault(), handleAddSkill())}
                            placeholder="Type a skill and press Enter or click Add"
                            className="rounded-2xl border-2 h-12 font-medium"
                          />
                          <Button
                            type="button"
                            onClick={handleAddSkill}
                            className="rounded-2xl font-bold px-6 gradient-primary hover:scale-105 transition-all duration-200"
                          >
                            Add
                          </Button>
                        </div>
                        <div className="flex flex-wrap gap-3">
                          {formData.skills?.map(skill => (
                            <Badge key={skill} className="flex items-center gap-2 px-4 py-2 rounded-full font-semibold bg-muted/50 hover:bg-muted text-foreground">
                              {skill}
                              <button
                                type="button"
                                onClick={() => handleRemoveSkill(skill)}
                                className="ml-1 hover:text-destructive transition-colors font-bold"
                              >
                                ×
                              </button>
                            </Badge>
                          ))}
                        </div>
                        {(!formData.skills || formData.skills.length === 0) && (
                          <p className="text-muted-foreground text-sm mt-2">
                            💡 Add skills like "JavaScript", "Project Management", "Graphic Design", etc.
                          </p>
                        )}
                      </div>
                    </div>

                    {/* Experience & Education Section */}
                    <div className="space-y-6">
                      <h3 className="text-xl font-black text-foreground flex items-center gap-2">
                        💼 Experience & Education
                      </h3>

                      <div>
                        <Label htmlFor="experience" className="text-sm font-bold text-foreground mb-3 block">
                          💼 Work Experience
                        </Label>
                        <Textarea
                          id="experience"
                          name="experience"
                          value={formData.experience}
                          onChange={handleInputChange}
                          rows={4}
                          placeholder="Describe your work experience, internships, projects..."
                          className="rounded-2xl border-2 font-medium resize-none"
                        />
                        <p className="text-xs text-muted-foreground mt-2">
                          💡 Include company names, roles, and key achievements
                        </p>
                      </div>

                      <div>
                        <Label htmlFor="education" className="text-sm font-bold text-foreground mb-3 block">
                          🎓 Education
                        </Label>
                        <Textarea
                          id="education"
                          name="education"
                          value={formData.education}
                          onChange={handleInputChange}
                          rows={3}
                          placeholder="Your educational background, degrees, certifications..."
                          className="rounded-2xl border-2 font-medium resize-none"
                        />
                        <p className="text-xs text-muted-foreground mt-2">
                          💡 Include school names, degrees, and graduation years
                        </p>
                      </div>
                    </div>

                    {/* Social Links (Optional) */}
                    <div className="space-y-6">
                      <div>
                        <h3 className="text-xl font-black text-foreground flex items-center gap-2">
                          🔗 Social Links (Optional)
                        </h3>
                        <p className="text-muted-foreground mt-2">
                          These are optional and won't affect your profile completion percentage.
                        </p>
                      </div>

                      <div className="grid md:grid-cols-1 gap-6">
                        <div>
                          <Label htmlFor="website" className="text-sm font-bold text-foreground mb-3 block">
                            🌐 Website
                          </Label>
                          <Input
                            id="website"
                            name="website"
                            value={formData.website}
                            onChange={handleInputChange}
                            placeholder="https://yourwebsite.com"
                            className={`rounded-2xl border-2 h-12 font-medium ${errors.website ? 'border-red-500' : ''}`}
                          />
                          {errors.website && <p className="text-red-600 text-sm mt-2 font-semibold">{errors.website}</p>}
                        </div>

                        <div>
                          <Label htmlFor="linkedin" className="text-sm font-bold text-foreground mb-3 block">
                            💼 LinkedIn
                          </Label>
                          <Input
                            id="linkedin"
                            name="linkedin"
                            value={formData.linkedin}
                            onChange={handleInputChange}
                            placeholder="https://linkedin.com/in/yourprofile"
                            className={`rounded-2xl border-2 h-12 font-medium ${errors.linkedin ? 'border-red-500' : ''}`}
                          />
                          {errors.linkedin && <p className="text-red-600 text-sm mt-2 font-semibold">{errors.linkedin}</p>}
                        </div>

                        <div>
                          <Label htmlFor="github" className="text-sm font-bold text-foreground mb-3 block">
                            💻 GitHub
                          </Label>
                          <Input
                            id="github"
                            name="github"
                            value={formData.github}
                            onChange={handleInputChange}
                            placeholder="https://github.com/yourusername"
                            className={`rounded-2xl border-2 h-12 font-medium ${errors.github ? 'border-red-500' : ''}`}
                          />
                          {errors.github && <p className="text-red-600 text-sm mt-2 font-semibold">{errors.github}</p>}
                        </div>
                      </div>
                    </div>

                    {/* Submit Button */}
                    <div className="pt-6">
                      <Button
                        type="submit"
                        className="w-full rounded-2xl font-bold h-14 text-lg gradient-primary hover:scale-105 transition-all duration-200"
                        disabled={updateProfileMutation.isPending}
                      >
                        {updateProfileMutation.isPending ? (
                          <>
                            <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                            Updating Profile...
                          </>
                        ) : (
                          <>
                            ✨ Update Profile
                          </>
                        )}
                      </Button>
                    </div>
                  </form>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function ProfilePage() {
  return (
    <Suspense fallback={<div className="min-h-screen flex items-center justify-center"><Loader2 className="h-8 w-8 animate-spin"/></div>}>
      <ProfilePageContent />
    </Suspense>
  )
}
