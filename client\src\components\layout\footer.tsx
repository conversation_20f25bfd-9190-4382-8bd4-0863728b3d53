import Link from 'next/link'
import { Briefcase, Mail, Phone, MapPin, Smartphone, Twitter } from 'lucide-react'

export function Footer() {
  const currentYear = new Date().getFullYear()

  const footerLinks = {
    company: [
      { name: 'About Us', href: '/about' },
      { name: 'Contact', href: '/contact' },
      { name: 'Blog', href: '/blogs' },
    ],
    jobSeekers: [
      { name: 'Browse Jobs', href: '/jobs' },
      { name: 'Career Advice', href: '/blogs' },
      { name: 'Resume Tips', href: '/blogs' },
    ],
    support: [
      { name: 'Help Center', href: '/contact' },
      { name: 'Privacy Policy', href: '/privacy' },
      { name: 'Terms of Service', href: '/terms' },
    ],
  }

  return (
    <footer className="bg-foreground text-background relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute top-0 left-0 w-64 h-64 bg-primary/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 right-0 w-80 h-80 bg-accent/10 rounded-full blur-3xl"></div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative">
        {/* Main Footer Content */}
        <div className="py-16 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12">
          {/* Brand Section */}
          <div className="space-y-6">
            <div className="flex items-center space-x-3">
              <div className="h-12 w-12 rounded-2xl gradient-primary flex items-center justify-center glow-primary">
                <Briefcase className="h-7 w-7 text-primary-foreground" />
              </div>
              <span className="text-2xl font-black text-background">
                Job<span className="text-primary">Portal</span>
              </span>
            </div>
            <p className="text-background/80 max-w-xs leading-relaxed font-medium">
              The professional job platform connecting talent with opportunity.
              <span className="text-accent font-bold"> Your career starts here.</span>
            </p>
            <div className="space-y-4">
              <div className="flex items-center space-x-3 text-background/70 font-medium">
                <Mail className="h-5 w-5 text-accent" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center space-x-3 text-background/70 font-medium">
                <Phone className="h-5 w-5 text-accent" />
                <span>+977 01-123-4567</span>
              </div>
              <div className="flex items-center space-x-3 text-background/70 font-medium">
                <MapPin className="h-5 w-5 text-accent" />
                <span>Kathmandu, Nepal</span>
              </div>
            </div>
          </div>

          {/* Company Links */}
          <div>
            <h3 className="font-bold text-background mb-6 text-lg">Company</h3>
            <ul className="space-y-4">
              {footerLinks.company.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-background/70 hover:text-accent transition-all duration-200 font-medium hover:translate-x-1"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Job Seekers Links */}
          <div>
            <h3 className="font-bold text-background mb-6 text-lg">For Job Seekers</h3>
            <ul className="space-y-4">
              {footerLinks.jobSeekers.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-background/70 hover:text-accent transition-all duration-200 font-medium hover:translate-x-1"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Support Links */}
          <div>
            <h3 className="font-bold text-background mb-6 text-lg">Support</h3>
            <ul className="space-y-4">
              {footerLinks.support.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-background/70 hover:text-accent transition-all duration-200 font-medium hover:translate-x-1"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Bottom Footer */}
        <div className="py-8 border-t border-background/20">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-6 md:space-y-0">
            <div className="text-center md:text-left">
              <p className="text-background/70 font-medium mb-2">
                © {currentYear} JobPortal. Connecting professionals with opportunities.
              </p>
              <p className="text-background/50 text-sm">
                Empowering careers through innovative job matching technology.
              </p>
            </div>
            <div className="flex items-center space-x-8">
              <Link
                href="/privacy"
                className="text-background/70 hover:text-accent transition-all duration-200 font-medium hover:scale-105"
              >
                Privacy
              </Link>
              <Link
                href="/terms"
                className="text-background/70 hover:text-accent transition-all duration-200 font-medium hover:scale-105"
              >
                Terms
              </Link>
              <div className="flex items-center space-x-4">
                <span className="text-background/50 text-sm">Follow us:</span>
                <div className="flex space-x-3">
                  <div className="w-8 h-8 rounded-full bg-background/10 flex items-center justify-center hover:bg-accent/20 transition-all duration-200 cursor-pointer">
                    <Smartphone className="h-4 w-4" />
                  </div>
                  <div className="w-8 h-8 rounded-full bg-background/10 flex items-center justify-center hover:bg-accent/20 transition-all duration-200 cursor-pointer">
                    <Briefcase className="h-4 w-4" />
                  </div>
                  <div className="w-8 h-8 rounded-full bg-background/10 flex items-center justify-center hover:bg-accent/20 transition-all duration-200 cursor-pointer">
                    <Twitter className="h-4 w-4" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
