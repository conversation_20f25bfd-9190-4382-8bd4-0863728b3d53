"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const db_1 = require("../lib/db");
const auth_1 = require("../middleware/auth");
const job_1 = require("../schemas/job");
const errorHandler_1 = require("../middleware/errorHandler");
const utils_1 = require("../lib/utils");
const router = express_1.default.Router();
router.get('/', async (req, res, next) => {
    try {
        const filters = job_1.jobFiltersSchema.parse(req.query);
        const { page, limit, skip } = (0, utils_1.getPaginationParams)(filters);
        const where = {
            isActive: true,
        };
        if (filters.category) {
            where.category = { contains: filters.category, mode: 'insensitive' };
        }
        if (filters.location) {
            where.location = { contains: filters.location, mode: 'insensitive' };
        }
        if (filters.jobType) {
            where.jobType = filters.jobType;
        }
        if (filters.experienceLevel) {
            where.experienceLevel = filters.experienceLevel;
        }
        if (filters.salaryMin || filters.salaryMax) {
            where.AND = [];
            if (filters.salaryMin) {
                where.AND.push({ salaryMin: { gte: filters.salaryMin } });
            }
            if (filters.salaryMax) {
                where.AND.push({ salaryMax: { lte: filters.salaryMax } });
            }
        }
        if (filters.search) {
            where.OR = [
                { title: { contains: filters.search, mode: 'insensitive' } },
                { description: { contains: filters.search, mode: 'insensitive' } },
                { companyName: { contains: filters.search, mode: 'insensitive' } },
            ];
        }
        const [jobs, total] = await Promise.all([
            db_1.db.job.findMany({
                where,
                select: {
                    id: true,
                    title: true,
                    slug: true,
                    description: true,
                    category: true,
                    location: true,
                    jobType: true,
                    workLocationType: true,
                    experienceLevel: true,
                    salaryMin: true,
                    salaryMax: true,
                    currency: true,
                    companyName: true,
                    companyLogo: true,
                    isFeatured: true,
                    applicationDeadline: true,
                    createdAt: true,
                    _count: {
                        select: { applications: true }
                    }
                },
                orderBy: [
                    { isFeatured: 'desc' },
                    { createdAt: 'desc' }
                ],
                skip,
                take: limit,
            }),
            db_1.db.job.count({ where })
        ]);
        const result = (0, utils_1.createPaginationResult)(jobs, total, page, limit);
        res.json({
            jobs: result.data,
            pagination: result.pagination
        });
    }
    catch (error) {
        next(error);
    }
});
router.get('/featured', async (req, res, next) => {
    try {
        const limit = Math.min(parseInt(req.query.limit) || 6, 20);
        const jobs = await db_1.db.job.findMany({
            where: {
                isActive: true,
                isFeatured: true,
            },
            select: {
                id: true,
                title: true,
                slug: true,
                description: true,
                category: true,
                location: true,
                jobType: true,
                workLocationType: true,
                experienceLevel: true,
                salaryMin: true,
                salaryMax: true,
                currency: true,
                companyName: true,
                companyLogo: true,
                isFeatured: true,
                createdAt: true,
            },
            orderBy: { createdAt: 'desc' },
            take: limit,
        });
        res.json({ jobs });
    }
    catch (error) {
        next(error);
    }
});
router.get('/categories', (req, res) => {
    res.json({
        categories: job_1.JOB_CATEGORIES,
        total: job_1.JOB_CATEGORIES.length
    });
});
router.get('/:slug', auth_1.optionalAuth, async (req, res, next) => {
    try {
        const { slug } = req.params;
        const job = await db_1.db.job.findUnique({
            where: { slug, isActive: true },
            include: {
                _count: {
                    select: { applications: true }
                }
            }
        });
        if (!job) {
            throw (0, errorHandler_1.createError)('Job not found', 404);
        }
        let hasApplied = false;
        if (req.user) {
            const application = await db_1.db.application.findUnique({
                where: {
                    userId_jobId: {
                        userId: req.user.id,
                        jobId: job.id,
                    }
                }
            });
            hasApplied = !!application;
        }
        res.json({ job, hasApplied });
    }
    catch (error) {
        next(error);
    }
});
router.post('/:jobId/apply', auth_1.authenticate, async (req, res, next) => {
    try {
        const { jobId } = req.params;
        const validatedData = job_1.applyJobSchema.parse(req.body);
        const user = await db_1.db.user.findUnique({
            where: { id: req.user.id },
            select: { profileCompleted: true }
        });
        if (!user?.profileCompleted) {
            throw (0, errorHandler_1.createError)('Please complete your profile and upload a resume before applying', 400);
        }
        const job = await db_1.db.job.findUnique({
            where: { id: jobId, isActive: true }
        });
        if (!job) {
            throw (0, errorHandler_1.createError)('Job not found or no longer active', 404);
        }
        const existingApplication = await db_1.db.application.findUnique({
            where: {
                userId_jobId: {
                    userId: req.user.id,
                    jobId: jobId,
                }
            }
        });
        if (existingApplication) {
            throw (0, errorHandler_1.createError)('You have already applied for this job', 409);
        }
        const application = await db_1.db.application.create({
            data: {
                userId: req.user.id,
                jobId: jobId,
                message: validatedData.message,
            },
            include: {
                job: {
                    select: {
                        title: true,
                        companyName: true,
                    }
                }
            }
        });
        res.status(201).json({
            message: 'Application submitted successfully',
            application,
        });
    }
    catch (error) {
        next(error);
    }
});
exports.default = router;
//# sourceMappingURL=jobs.js.map