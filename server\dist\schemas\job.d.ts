import { z } from 'zod';
export declare const JOB_CATEGORIES: readonly ["Information Technology (IT)", "Software Development", "Sales & Marketing", "Accounting & Finance", "Design & Multimedia", "Content Writing & Editing", "Customer Service & Support", "Human Resources (HR)", "Education & Teaching", "Engineering", "Internships", "Administration & Operations", "Digital Marketing", "Project Management", "NGO & INGO", "Healthcare & Medical", "Data Entry & Back Office", "Banking & Insurance", "Hospitality & Tourism", "Legal & Compliance", "Logistics & Procurement", "Media & Communication", "Business Development", "Remote Jobs", "Other / Miscellaneous"];
export declare const createJobSchema: z.ZodObject<{
    title: z.ZodString;
    description: z.ZodString;
    requirements: z.<PERSON>od<PERSON><PERSON><PERSON><z.Zod<PERSON>rray<z.ZodString, "many">>;
    responsibilities: z.Zod<PERSON>efault<z.ZodArray<z.ZodString, "many">>;
    category: z.<PERSON>od<PERSON>num<["Information Technology (IT)", "Software Development", "Sales & Marketing", "Accounting & Finance", "Design & Multimedia", "Content Writing & Editing", "Customer Service & Support", "Human Resources (HR)", "Education & Teaching", "Engineering", "Internships", "Administration & Operations", "Digital Marketing", "Project Management", "NGO & INGO", "Healthcare & Medical", "Data Entry & Back Office", "Banking & Insurance", "Hospitality & Tourism", "Legal & Compliance", "Logistics & Procurement", "Media & Communication", "Business Development", "Remote Jobs", "Other / Miscellaneous"]>;
    location: z.ZodString;
    jobType: z.ZodEnum<["FULL_TIME", "PART_TIME", "CONTRACT", "INTERNSHIP"]>;
    workLocationType: z.ZodDefault<z.ZodEnum<["ONSITE", "REMOTE", "HYBRID"]>>;
    experienceLevel: z.ZodEnum<["STUDENT", "FRESHER", "INTERNSHIP_ONLY", "ZERO_TO_ONE_YEAR", "ONE_TO_THREE_YEARS", "THREE_TO_FIVE_YEARS", "FIVE_PLUS_YEARS"]>;
    salaryMin: z.ZodOptional<z.ZodNumber>;
    salaryMax: z.ZodOptional<z.ZodNumber>;
    salaryNegotiable: z.ZodDefault<z.ZodOptional<z.ZodBoolean>>;
    currency: z.ZodDefault<z.ZodString>;
    companyName: z.ZodString;
    companyWebsite: z.ZodOptional<z.ZodString>;
    companyLogo: z.ZodEffects<z.ZodOptional<z.ZodString>, string | undefined, string | undefined>;
    metaTitle: z.ZodOptional<z.ZodString>;
    metaDescription: z.ZodOptional<z.ZodString>;
    isActive: z.ZodDefault<z.ZodBoolean>;
    isFeatured: z.ZodDefault<z.ZodBoolean>;
    applicationDeadline: z.ZodEffects<z.ZodOptional<z.ZodString>, string | undefined, string | undefined>;
}, "strip", z.ZodTypeAny, {
    experienceLevel: "STUDENT" | "FRESHER" | "INTERNSHIP_ONLY" | "ZERO_TO_ONE_YEAR" | "ONE_TO_THREE_YEARS" | "THREE_TO_FIVE_YEARS" | "FIVE_PLUS_YEARS";
    location: string;
    title: string;
    description: string;
    requirements: string[];
    responsibilities: string[];
    category: "Information Technology (IT)" | "Software Development" | "Sales & Marketing" | "Accounting & Finance" | "Design & Multimedia" | "Content Writing & Editing" | "Customer Service & Support" | "Human Resources (HR)" | "Education & Teaching" | "Engineering" | "Internships" | "Administration & Operations" | "Digital Marketing" | "Project Management" | "NGO & INGO" | "Healthcare & Medical" | "Data Entry & Back Office" | "Banking & Insurance" | "Hospitality & Tourism" | "Legal & Compliance" | "Logistics & Procurement" | "Media & Communication" | "Business Development" | "Remote Jobs" | "Other / Miscellaneous";
    jobType: "FULL_TIME" | "PART_TIME" | "CONTRACT" | "INTERNSHIP";
    workLocationType: "ONSITE" | "REMOTE" | "HYBRID";
    salaryNegotiable: boolean;
    currency: string;
    companyName: string;
    isActive: boolean;
    isFeatured: boolean;
    salaryMin?: number | undefined;
    salaryMax?: number | undefined;
    companyWebsite?: string | undefined;
    companyLogo?: string | undefined;
    metaTitle?: string | undefined;
    metaDescription?: string | undefined;
    applicationDeadline?: string | undefined;
}, {
    experienceLevel: "STUDENT" | "FRESHER" | "INTERNSHIP_ONLY" | "ZERO_TO_ONE_YEAR" | "ONE_TO_THREE_YEARS" | "THREE_TO_FIVE_YEARS" | "FIVE_PLUS_YEARS";
    location: string;
    title: string;
    description: string;
    category: "Information Technology (IT)" | "Software Development" | "Sales & Marketing" | "Accounting & Finance" | "Design & Multimedia" | "Content Writing & Editing" | "Customer Service & Support" | "Human Resources (HR)" | "Education & Teaching" | "Engineering" | "Internships" | "Administration & Operations" | "Digital Marketing" | "Project Management" | "NGO & INGO" | "Healthcare & Medical" | "Data Entry & Back Office" | "Banking & Insurance" | "Hospitality & Tourism" | "Legal & Compliance" | "Logistics & Procurement" | "Media & Communication" | "Business Development" | "Remote Jobs" | "Other / Miscellaneous";
    jobType: "FULL_TIME" | "PART_TIME" | "CONTRACT" | "INTERNSHIP";
    companyName: string;
    requirements?: string[] | undefined;
    responsibilities?: string[] | undefined;
    workLocationType?: "ONSITE" | "REMOTE" | "HYBRID" | undefined;
    salaryMin?: number | undefined;
    salaryMax?: number | undefined;
    salaryNegotiable?: boolean | undefined;
    currency?: string | undefined;
    companyWebsite?: string | undefined;
    companyLogo?: string | undefined;
    metaTitle?: string | undefined;
    metaDescription?: string | undefined;
    isActive?: boolean | undefined;
    isFeatured?: boolean | undefined;
    applicationDeadline?: string | undefined;
}>;
export declare const updateJobSchema: z.ZodObject<{
    title: z.ZodOptional<z.ZodString>;
    description: z.ZodOptional<z.ZodString>;
    requirements: z.ZodOptional<z.ZodDefault<z.ZodArray<z.ZodString, "many">>>;
    responsibilities: z.ZodOptional<z.ZodDefault<z.ZodArray<z.ZodString, "many">>>;
    category: z.ZodOptional<z.ZodEnum<["Information Technology (IT)", "Software Development", "Sales & Marketing", "Accounting & Finance", "Design & Multimedia", "Content Writing & Editing", "Customer Service & Support", "Human Resources (HR)", "Education & Teaching", "Engineering", "Internships", "Administration & Operations", "Digital Marketing", "Project Management", "NGO & INGO", "Healthcare & Medical", "Data Entry & Back Office", "Banking & Insurance", "Hospitality & Tourism", "Legal & Compliance", "Logistics & Procurement", "Media & Communication", "Business Development", "Remote Jobs", "Other / Miscellaneous"]>>;
    location: z.ZodOptional<z.ZodString>;
    jobType: z.ZodOptional<z.ZodEnum<["FULL_TIME", "PART_TIME", "CONTRACT", "INTERNSHIP"]>>;
    workLocationType: z.ZodOptional<z.ZodDefault<z.ZodEnum<["ONSITE", "REMOTE", "HYBRID"]>>>;
    experienceLevel: z.ZodOptional<z.ZodEnum<["STUDENT", "FRESHER", "INTERNSHIP_ONLY", "ZERO_TO_ONE_YEAR", "ONE_TO_THREE_YEARS", "THREE_TO_FIVE_YEARS", "FIVE_PLUS_YEARS"]>>;
    salaryMin: z.ZodOptional<z.ZodOptional<z.ZodNumber>>;
    salaryMax: z.ZodOptional<z.ZodOptional<z.ZodNumber>>;
    salaryNegotiable: z.ZodOptional<z.ZodDefault<z.ZodOptional<z.ZodBoolean>>>;
    currency: z.ZodOptional<z.ZodDefault<z.ZodString>>;
    companyName: z.ZodOptional<z.ZodString>;
    companyWebsite: z.ZodOptional<z.ZodOptional<z.ZodString>>;
    companyLogo: z.ZodOptional<z.ZodEffects<z.ZodOptional<z.ZodString>, string | undefined, string | undefined>>;
    metaTitle: z.ZodOptional<z.ZodOptional<z.ZodString>>;
    metaDescription: z.ZodOptional<z.ZodOptional<z.ZodString>>;
    isActive: z.ZodOptional<z.ZodDefault<z.ZodBoolean>>;
    isFeatured: z.ZodOptional<z.ZodDefault<z.ZodBoolean>>;
    applicationDeadline: z.ZodOptional<z.ZodEffects<z.ZodOptional<z.ZodString>, string | undefined, string | undefined>>;
}, "strip", z.ZodTypeAny, {
    experienceLevel?: "STUDENT" | "FRESHER" | "INTERNSHIP_ONLY" | "ZERO_TO_ONE_YEAR" | "ONE_TO_THREE_YEARS" | "THREE_TO_FIVE_YEARS" | "FIVE_PLUS_YEARS" | undefined;
    location?: string | undefined;
    title?: string | undefined;
    description?: string | undefined;
    requirements?: string[] | undefined;
    responsibilities?: string[] | undefined;
    category?: "Information Technology (IT)" | "Software Development" | "Sales & Marketing" | "Accounting & Finance" | "Design & Multimedia" | "Content Writing & Editing" | "Customer Service & Support" | "Human Resources (HR)" | "Education & Teaching" | "Engineering" | "Internships" | "Administration & Operations" | "Digital Marketing" | "Project Management" | "NGO & INGO" | "Healthcare & Medical" | "Data Entry & Back Office" | "Banking & Insurance" | "Hospitality & Tourism" | "Legal & Compliance" | "Logistics & Procurement" | "Media & Communication" | "Business Development" | "Remote Jobs" | "Other / Miscellaneous" | undefined;
    jobType?: "FULL_TIME" | "PART_TIME" | "CONTRACT" | "INTERNSHIP" | undefined;
    workLocationType?: "ONSITE" | "REMOTE" | "HYBRID" | undefined;
    salaryMin?: number | undefined;
    salaryMax?: number | undefined;
    salaryNegotiable?: boolean | undefined;
    currency?: string | undefined;
    companyName?: string | undefined;
    companyWebsite?: string | undefined;
    companyLogo?: string | undefined;
    metaTitle?: string | undefined;
    metaDescription?: string | undefined;
    isActive?: boolean | undefined;
    isFeatured?: boolean | undefined;
    applicationDeadline?: string | undefined;
}, {
    experienceLevel?: "STUDENT" | "FRESHER" | "INTERNSHIP_ONLY" | "ZERO_TO_ONE_YEAR" | "ONE_TO_THREE_YEARS" | "THREE_TO_FIVE_YEARS" | "FIVE_PLUS_YEARS" | undefined;
    location?: string | undefined;
    title?: string | undefined;
    description?: string | undefined;
    requirements?: string[] | undefined;
    responsibilities?: string[] | undefined;
    category?: "Information Technology (IT)" | "Software Development" | "Sales & Marketing" | "Accounting & Finance" | "Design & Multimedia" | "Content Writing & Editing" | "Customer Service & Support" | "Human Resources (HR)" | "Education & Teaching" | "Engineering" | "Internships" | "Administration & Operations" | "Digital Marketing" | "Project Management" | "NGO & INGO" | "Healthcare & Medical" | "Data Entry & Back Office" | "Banking & Insurance" | "Hospitality & Tourism" | "Legal & Compliance" | "Logistics & Procurement" | "Media & Communication" | "Business Development" | "Remote Jobs" | "Other / Miscellaneous" | undefined;
    jobType?: "FULL_TIME" | "PART_TIME" | "CONTRACT" | "INTERNSHIP" | undefined;
    workLocationType?: "ONSITE" | "REMOTE" | "HYBRID" | undefined;
    salaryMin?: number | undefined;
    salaryMax?: number | undefined;
    salaryNegotiable?: boolean | undefined;
    currency?: string | undefined;
    companyName?: string | undefined;
    companyWebsite?: string | undefined;
    companyLogo?: string | undefined;
    metaTitle?: string | undefined;
    metaDescription?: string | undefined;
    isActive?: boolean | undefined;
    isFeatured?: boolean | undefined;
    applicationDeadline?: string | undefined;
}>;
export declare const jobFiltersSchema: z.ZodObject<{
    category: z.ZodOptional<z.ZodString>;
    location: z.ZodOptional<z.ZodString>;
    jobType: z.ZodOptional<z.ZodEnum<["FULL_TIME", "PART_TIME", "CONTRACT", "INTERNSHIP"]>>;
    workLocationType: z.ZodOptional<z.ZodEnum<["ONSITE", "REMOTE", "HYBRID"]>>;
    experienceLevel: z.ZodOptional<z.ZodEnum<["STUDENT", "FRESHER", "INTERNSHIP_ONLY", "ZERO_TO_ONE_YEAR", "ONE_TO_THREE_YEARS", "THREE_TO_FIVE_YEARS", "FIVE_PLUS_YEARS"]>>;
    salaryMin: z.ZodOptional<z.ZodEffects<z.ZodUnion<[z.ZodString, z.ZodNumber]>, number | undefined, string | number>>;
    salaryMax: z.ZodOptional<z.ZodEffects<z.ZodUnion<[z.ZodString, z.ZodNumber]>, number | undefined, string | number>>;
    search: z.ZodOptional<z.ZodString>;
    page: z.ZodDefault<z.ZodEffects<z.ZodUnion<[z.ZodString, z.ZodNumber]>, number, string | number>>;
    limit: z.ZodDefault<z.ZodEffects<z.ZodUnion<[z.ZodString, z.ZodNumber]>, number, string | number>>;
}, "strip", z.ZodTypeAny, {
    page: number;
    limit: number;
    search?: string | undefined;
    experienceLevel?: "STUDENT" | "FRESHER" | "INTERNSHIP_ONLY" | "ZERO_TO_ONE_YEAR" | "ONE_TO_THREE_YEARS" | "THREE_TO_FIVE_YEARS" | "FIVE_PLUS_YEARS" | undefined;
    location?: string | undefined;
    category?: string | undefined;
    jobType?: "FULL_TIME" | "PART_TIME" | "CONTRACT" | "INTERNSHIP" | undefined;
    workLocationType?: "ONSITE" | "REMOTE" | "HYBRID" | undefined;
    salaryMin?: number | undefined;
    salaryMax?: number | undefined;
}, {
    search?: string | undefined;
    experienceLevel?: "STUDENT" | "FRESHER" | "INTERNSHIP_ONLY" | "ZERO_TO_ONE_YEAR" | "ONE_TO_THREE_YEARS" | "THREE_TO_FIVE_YEARS" | "FIVE_PLUS_YEARS" | undefined;
    location?: string | undefined;
    category?: string | undefined;
    jobType?: "FULL_TIME" | "PART_TIME" | "CONTRACT" | "INTERNSHIP" | undefined;
    workLocationType?: "ONSITE" | "REMOTE" | "HYBRID" | undefined;
    salaryMin?: string | number | undefined;
    salaryMax?: string | number | undefined;
    page?: string | number | undefined;
    limit?: string | number | undefined;
}>;
export declare const applyJobSchema: z.ZodObject<{
    message: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    message?: string | undefined;
}, {
    message?: string | undefined;
}>;
export type CreateJobInput = z.infer<typeof createJobSchema>;
export type UpdateJobInput = z.infer<typeof updateJobSchema>;
export type JobFiltersInput = z.infer<typeof jobFiltersSchema>;
export type ApplyJobInput = z.infer<typeof applyJobSchema>;
//# sourceMappingURL=job.d.ts.map