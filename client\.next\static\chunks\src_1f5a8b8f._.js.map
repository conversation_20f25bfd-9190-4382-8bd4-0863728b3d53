{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/job-portal/client/src/components/loading.tsx"], "sourcesContent": ["import { Loader2 } from 'lucide-react'\nimport { Card, CardContent } from '@/components/ui/card'\n\ninterface LoadingProps {\n  size?: 'sm' | 'md' | 'lg'\n  text?: string\n  fullScreen?: boolean\n  card?: boolean\n}\n\nexport function Loading({\n  size = 'md',\n  text = 'Loading...',\n  fullScreen = false,\n  card = false\n}: LoadingProps) {\n  const sizeClasses = {\n    sm: 'h-4 w-4',\n    md: 'h-8 w-8',\n    lg: 'h-12 w-12'\n  }\n\n  const content = (\n    <div className=\"flex flex-col items-center justify-center gap-3\">\n      <Loader2 className={`${sizeClasses[size]} animate-spin text-primary`} />\n      {text && (\n        <p className=\"text-sm text-muted-foreground\">{text}</p>\n      )}\n    </div>\n  )\n\n  if (fullScreen) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        {content}\n      </div>\n    )\n  }\n\n  if (card) {\n    return (\n      <Card>\n        <CardContent className=\"p-8\">\n          {content}\n        </CardContent>\n      </Card>\n    )\n  }\n\n  return (\n    <div className=\"flex items-center justify-center py-8\">\n      {content}\n    </div>\n  )\n}\n\n// Skeleton components for better loading UX\nexport function JobCardSkeleton() {\n  return (\n    <Card className=\"glass border-0 h-full flex flex-col overflow-hidden\">\n      <CardContent className=\"p-6 flex-1 flex flex-col\">\n        <div className=\"space-y-6 flex-1\">\n          {/* Header */}\n          <div className=\"flex items-start justify-between\">\n            <div className=\"space-y-3 flex-1\">\n              <div className=\"h-6 bg-gradient-to-r from-muted to-muted/50 rounded-2xl animate-pulse\" />\n              <div className=\"flex items-center gap-3\">\n                <div className=\"w-8 h-8 bg-gradient-to-r from-muted to-muted/50 rounded-xl animate-pulse\" />\n                <div className=\"h-4 bg-gradient-to-r from-muted to-muted/50 rounded-full w-2/3 animate-pulse\" />\n              </div>\n            </div>\n            <div className=\"h-8 w-12 bg-gradient-to-r from-accent/20 to-accent/10 rounded-full animate-pulse\" />\n          </div>\n\n          {/* Tags */}\n          <div className=\"flex items-center gap-3\">\n            <div className=\"h-7 w-20 bg-gradient-to-r from-primary/20 to-primary/10 rounded-full animate-pulse\" />\n            <div className=\"h-7 w-16 bg-gradient-to-r from-accent/20 to-accent/10 rounded-full animate-pulse\" />\n          </div>\n\n          {/* Salary */}\n          <div className=\"h-10 bg-gradient-to-r from-green-500/20 to-emerald-500/10 rounded-2xl animate-pulse\" />\n\n          {/* Description */}\n          <div className=\"space-y-2\">\n            <div className=\"h-3 bg-gradient-to-r from-muted to-muted/50 rounded animate-pulse\" />\n            <div className=\"h-3 bg-gradient-to-r from-muted to-muted/50 rounded w-4/5 animate-pulse\" />\n            <div className=\"h-3 bg-gradient-to-r from-muted to-muted/50 rounded w-3/5 animate-pulse\" />\n          </div>\n\n          {/* Badges */}\n          <div className=\"flex gap-2\">\n            <div className=\"h-6 w-20 bg-gradient-to-r from-muted to-muted/50 rounded-full animate-pulse\" />\n            <div className=\"h-6 w-16 bg-gradient-to-r from-muted to-muted/50 rounded-full animate-pulse\" />\n          </div>\n        </div>\n\n        {/* Button */}\n        <div className=\"mt-6\">\n          <div className=\"h-12 bg-gradient-to-r from-primary/30 to-primary/20 rounded-2xl animate-pulse\" />\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n\nexport function ApplicationCardSkeleton() {\n  return (\n    <Card>\n      <CardContent className=\"p-6\">\n        <div className=\"space-y-4\">\n          <div className=\"flex items-start justify-between\">\n            <div className=\"space-y-2 flex-1\">\n              <div className=\"h-6 bg-muted rounded animate-pulse\" />\n              <div className=\"h-4 bg-muted rounded w-1/2 animate-pulse\" />\n              <div className=\"h-4 bg-muted rounded w-3/4 animate-pulse\" />\n            </div>\n            <div className=\"h-6 w-20 bg-muted rounded animate-pulse\" />\n          </div>\n          <div className=\"h-4 bg-muted rounded w-1/3 animate-pulse\" />\n          <div className=\"h-16 bg-muted rounded animate-pulse\" />\n          <div className=\"flex gap-2\">\n            <div className=\"h-9 w-24 bg-muted rounded animate-pulse\" />\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n\nexport function ProfileSkeleton() {\n  return (\n    <div className=\"space-y-6\">\n      <Card>\n        <CardContent className=\"p-6\">\n          <div className=\"flex items-center gap-4\">\n            <div className=\"h-16 w-16 bg-muted rounded-full animate-pulse\" />\n            <div className=\"space-y-2 flex-1\">\n              <div className=\"h-5 bg-muted rounded animate-pulse\" />\n              <div className=\"h-4 bg-muted rounded w-2/3 animate-pulse\" />\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      <Card>\n        <CardContent className=\"p-6\">\n          <div className=\"space-y-4\">\n            <div className=\"h-6 bg-muted rounded w-1/3 animate-pulse\" />\n            <div className=\"space-y-3\">\n              {Array.from({ length: 5 }).map((_, i) => (\n                <div key={i} className=\"flex items-center gap-3\">\n                  <div className=\"h-4 w-4 bg-muted rounded animate-pulse\" />\n                  <div className=\"h-4 bg-muted rounded w-1/2 animate-pulse\" />\n                </div>\n              ))}\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;;AASO,SAAS,QAAQ,EACtB,OAAO,IAAI,EACX,OAAO,YAAY,EACnB,aAAa,KAAK,EAClB,OAAO,KAAK,EACC;IACb,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,wBACJ,6LAAC;QAAI,WAAU;;0BACb,6LAAC,oNAAA,CAAA,UAAO;gBAAC,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,0BAA0B,CAAC;;;;;;YACnE,sBACC,6LAAC;gBAAE,WAAU;0BAAiC;;;;;;;;;;;;IAKpD,IAAI,YAAY;QACd,qBACE,6LAAC;YAAI,WAAU;sBACZ;;;;;;IAGP;IAEA,IAAI,MAAM;QACR,qBACE,6LAAC,mIAAA,CAAA,OAAI;sBACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;0BACpB;;;;;;;;;;;IAIT;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACZ;;;;;;AAGP;KA5CgB;AA+CT,SAAS;IACd,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;kBACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;YAAC,WAAU;;8BACrB,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;;8CAGnB,6LAAC;oCAAI,WAAU;;;;;;;;;;;;sCAIjB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;;;;;;;sCAIjB,6LAAC;4BAAI,WAAU;;;;;;sCAGf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;;;;;;;sCAIjB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;8BAKnB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;AAKzB;MA/CgB;AAiDT,SAAS;IACd,qBACE,6LAAC,mIAAA,CAAA,OAAI;kBACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;YAAC,WAAU;sBACrB,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;;;;;;;0CAEjB,6LAAC;gCAAI,WAAU;;;;;;;;;;;;kCAEjB,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3B;MAtBgB;AAwBT,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,mIAAA,CAAA,OAAI;0BACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMvB,6LAAC,mIAAA,CAAA,OAAI;0BACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;0CACZ,MAAM,IAAI,CAAC;oCAAE,QAAQ;gCAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,6LAAC;wCAAY,WAAU;;0DACrB,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;;uCAFP;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW1B;MAhCgB", "debugId": null}}, {"offset": {"line": 503, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/job-portal/client/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 535, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/job-portal/client/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 784, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/job-portal/client/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 836, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/job-portal/client/src/components/jobs/job-card.tsx"], "sourcesContent": ["import Link from 'next/link'\nimport { <PERSON><PERSON> } from '@/components/ui/button'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Building, MapPin, Clock, DollarSign } from 'lucide-react'\nimport { Job, formatSalary, getJobTypeLabel, getExperienceLabel, getWorkTypeLabel, formatDate } from '@/lib/api'\n\ninterface JobCardProps {\n  job: Job\n}\n\nexport function JobCard({ job }: JobCardProps) {\n  return (\n    <Card className=\"glass border-0 hover:shadow-lg transition-all duration-300 h-full flex flex-col group overflow-hidden\">\n      <CardHeader className=\"pb-4 relative\">\n        {job.isFeatured && (\n          <div className=\"absolute -top-2 -right-2 w-16 h-16\">\n            <div className=\"absolute inset-0 bg-gradient-to-br from-accent to-primary rounded-full\"></div>\n            <div className=\"absolute inset-1 bg-background rounded-full flex items-center justify-center\">\n              <span className=\"text-xs font-bold text-accent\">⭐</span>\n            </div>\n          </div>\n        )}\n\n        <div className=\"flex items-start justify-between\">\n          <div className=\"flex-1\">\n            <CardTitle className=\"text-xl font-bold mb-3 line-clamp-2 group-hover:text-primary transition-colors\">\n              {job.title}\n            </CardTitle>\n            <div className=\"flex items-center text-muted-foreground mb-2\">\n              <div className=\"w-8 h-8 rounded-xl bg-primary/10 flex items-center justify-center mr-3\">\n                <Building className=\"h-4 w-4 text-primary\" />\n              </div>\n              <span className=\"font-semibold\">{job.companyName}</span>\n            </div>\n          </div>\n        </div>\n      </CardHeader>\n\n      <CardContent className=\"flex-1 flex flex-col p-6\">\n        <div className=\"space-y-4 flex-1\">\n          {/* Location and Work Type */}\n          <div className=\"flex items-center gap-3\">\n            <div className=\"flex items-center gap-2 px-3 py-1.5 bg-muted/50 rounded-full\">\n              <MapPin className=\"h-3 w-3 text-primary\" />\n              <span className=\"text-xs font-semibold\">{job.location}</span>\n            </div>\n            <div className=\"flex items-center gap-2 px-3 py-1.5 bg-accent/10 rounded-full\">\n              <span className=\"text-xs font-semibold text-accent\">{getWorkTypeLabel(job.workLocationType || '')}</span>\n            </div>\n          </div>\n\n          {/* Job Type and Experience */}\n          <div className=\"flex items-center gap-3\">\n            <div className=\"flex items-center gap-2 px-3 py-1.5 bg-primary/10 rounded-full\">\n              <Clock className=\"h-3 w-3 text-primary\" />\n              <span className=\"text-xs font-semibold text-primary\">{getJobTypeLabel(job.jobType)}</span>\n            </div>\n            <div className=\"flex items-center gap-2 px-3 py-1.5 bg-secondary/50 rounded-full\">\n              <span className=\"text-xs font-semibold\">{getExperienceLabel(job.experienceLevel)}</span>\n            </div>\n          </div>\n\n          {/* Salary */}\n          {(job.salaryMin || job.salaryMax || job.salaryNegotiable) && (\n            <div className=\"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-2xl border border-green-500/20\">\n              <DollarSign className=\"h-4 w-4 text-green-600\" />\n              <span className=\"font-bold text-green-700\">{formatSalary(job.salaryMin, job.salaryMax, job.salaryNegotiable)}</span>\n            </div>\n          )}\n\n          {/* Description Preview */}\n          {job.description && (\n            <p className=\"text-sm text-muted-foreground line-clamp-3 leading-relaxed\">\n              {job.description.replace(/<[^>]*>/g, '').substring(0, 120)}...\n            </p>\n          )}\n\n          {/* Category and Posted Date */}\n          <div className=\"flex flex-wrap gap-2 pt-2\">\n            <Badge variant=\"outline\" className=\"rounded-full font-semibold\">{job.category}</Badge>\n            <Badge variant=\"outline\" className=\"text-xs rounded-full\">\n              {formatDate(job.createdAt)}\n            </Badge>\n          </div>\n        </div>\n\n        {/* Action Button */}\n        <div className=\"mt-6\">\n          <Button asChild className=\"w-full rounded-lg font-semibold h-12 gradient-primary hover:shadow-lg transition-all duration-200\">\n            <Link href={`/jobs/${job.slug}`}>\n              View Details →\n            </Link>\n          </Button>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;;;;;;;;AAMO,SAAS,QAAQ,EAAE,GAAG,EAAgB;IAC3C,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6LAAC,mIAAA,CAAA,aAAU;gBAAC,WAAU;;oBACnB,IAAI,UAAU,kBACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAAgC;;;;;;;;;;;;;;;;;kCAKtD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;8CAClB,IAAI,KAAK;;;;;;8CAEZ,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,6LAAC;4CAAK,WAAU;sDAAiB,IAAI,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMxD,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;gDAAK,WAAU;0DAAyB,IAAI,QAAQ;;;;;;;;;;;;kDAEvD,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDAAqC,CAAA,GAAA,oHAAA,CAAA,mBAAgB,AAAD,EAAE,IAAI,gBAAgB,IAAI;;;;;;;;;;;;;;;;;0CAKlG,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;gDAAK,WAAU;0DAAsC,CAAA,GAAA,oHAAA,CAAA,kBAAe,AAAD,EAAE,IAAI,OAAO;;;;;;;;;;;;kDAEnF,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDAAyB,CAAA,GAAA,oHAAA,CAAA,qBAAkB,AAAD,EAAE,IAAI,eAAe;;;;;;;;;;;;;;;;;4BAKlF,CAAC,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,IAAI,gBAAgB,mBACtD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,6LAAC;wCAAK,WAAU;kDAA4B,CAAA,GAAA,oHAAA,CAAA,eAAY,AAAD,EAAE,IAAI,SAAS,EAAE,IAAI,SAAS,EAAE,IAAI,gBAAgB;;;;;;;;;;;;4BAK9G,IAAI,WAAW,kBACd,6LAAC;gCAAE,WAAU;;oCACV,IAAI,WAAW,CAAC,OAAO,CAAC,YAAY,IAAI,SAAS,CAAC,GAAG;oCAAK;;;;;;;0CAK/D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAU,WAAU;kDAA8B,IAAI,QAAQ;;;;;;kDAC7E,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAU,WAAU;kDAChC,CAAA,GAAA,oHAAA,CAAA,aAAU,AAAD,EAAE,IAAI,SAAS;;;;;;;;;;;;;;;;;;kCAM/B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;4BAAC,OAAO;4BAAC,WAAU;sCACxB,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAM,CAAC,MAAM,EAAE,IAAI,IAAI,EAAE;0CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ7C;KAvFgB", "debugId": null}}, {"offset": {"line": 1173, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/job-portal/client/src/app/jobs/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, Suspense } from 'react'\nimport { useSearchParams } from 'next/navigation'\nimport { useQuery } from '@tanstack/react-query'\nimport { api, JobFilters } from '@/lib/api'\nimport { JobCardSkeleton } from '@/components/loading'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { JobCard } from '@/components/jobs/job-card'\nimport { Search, Filter, X, Loader2 } from 'lucide-react'\n\nfunction JobsContent() {\n  const searchParams = useSearchParams()\n  const [showFilters, setShowFilters] = useState(false)\n  const [filters, setFilters] = useState<JobFilters>({\n    search: searchParams.get('search') || '',\n    category: searchParams.get('category') || '',\n    location: searchParams.get('location') || '',\n    workLocationType: searchParams.get('workLocationType') || '',\n    jobType: searchParams.get('jobType') || '',\n    experienceLevel: searchParams.get('experienceLevel') || '',\n    salaryMin: searchParams.get('salaryMin') ? parseInt(searchParams.get('salaryMin')!) : undefined,\n    salaryMax: searchParams.get('salaryMax') ? parseInt(searchParams.get('salaryMax')!) : undefined,\n  })\n  const [currentPage, setCurrentPage] = useState(1)\n\n  const { data, isLoading, error } = useQuery({\n    queryKey: ['jobs', filters, currentPage],\n    queryFn: () => api.getJobs(filters, currentPage, 12),\n  })\n\n  if(!isLoading){\n    console.log(data)\n  }\n\n  const { data: categoriesData } = useQuery({\n    queryKey: ['job-categories'],\n    queryFn: () => api.getJobCategories(),\n  })\n\n  const categories = categoriesData?.categories || []\n\n  const jobTypes = [\n    { value: 'FULL_TIME', label: 'Full Time' },\n    { value: 'PART_TIME', label: 'Part Time' },\n    { value: 'CONTRACT', label: 'Contract' },\n    { value: 'INTERNSHIP', label: 'Internship' },\n  ]\n\n  const workTypes = [\n    { value: 'ONSITE', label: 'On-site' },\n    { value: 'REMOTE', label: 'Remote' },\n    { value: 'HYBRID', label: 'Hybrid' },\n  ]\n\n  const experienceLevels = [\n    { value: 'STUDENT', label: 'Student / Currently Studying' },\n    { value: 'FRESHER', label: 'Fresher' },\n    { value: 'INTERNSHIP_ONLY', label: 'Internship Experience Only' },\n    { value: 'ZERO_TO_ONE_YEAR', label: '0–1 Year' },\n    { value: 'ONE_TO_THREE_YEARS', label: '1–3 Years' },\n    { value: 'THREE_TO_FIVE_YEARS', label: '3–5 Years' },\n    { value: 'FIVE_PLUS_YEARS', label: '5+ Years' },\n  ]\n\n  const handleFilterChange = (key: keyof JobFilters, value: string | number | undefined) => {\n    setFilters(prev => ({ ...prev, [key]: value === '' || value === 'all' ? undefined : value }))\n    setCurrentPage(1)\n  }\n\n  const clearFilters = () => {\n    setFilters({})\n    setCurrentPage(1)\n  }\n\n  const hasActiveFilters = Object.values(filters).some(value => value !== undefined && value !== '')\n\n  const handlePageChange = (page: number) => {\n    setCurrentPage(page)\n    window.scrollTo({ top: 0, behavior: 'smooth' })\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-background via-muted/10 to-secondary/5\">\n      {/* Header */}\n      <section className=\"relative py-20 lg:py-32 overflow-hidden\">\n        {/* Background Elements */}\n        <div className=\"absolute inset-0 bg-gradient-to-br from-background via-muted/20 to-secondary/10\"></div>\n        <div className=\"absolute top-20 left-10 w-72 h-72 bg-primary/10 rounded-full blur-3xl float\"></div>\n        <div className=\"absolute bottom-20 right-10 w-96 h-96 bg-accent/10 rounded-full blur-3xl float\" style={{animationDelay: '2s'}}></div>\n\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative\">\n          <div className=\"max-w-5xl mx-auto text-center\">\n            <div className=\"mb-8\">\n              <span className=\"inline-block px-4 py-2 bg-primary/10 text-primary font-semibold rounded-full text-sm mb-6\">\n                🌟 {data ? `${data.pagination.total} opportunities available` : 'Loading opportunities...'}\n              </span>\n            </div>\n            <h1 className=\"text-5xl md:text-7xl lg:text-8xl font-bold text-foreground mb-8 leading-tight\">\n              Professional{' '}\n              <span className=\"gradient-primary bg-clip-text text-transparent\">\n                Opportunities\n              </span>\n            </h1>\n            <p className=\"text-xl md:text-2xl text-muted-foreground mb-12 max-w-3xl mx-auto leading-relaxed\">\n              Discover career opportunities at leading companies with excellent workplace cultures.\n              <span className=\"text-foreground font-semibold\"> Your next role awaits.</span>\n            </p>\n          </div>\n        </div>\n      </section>\n\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Search and Filters */}\n        <div className=\"space-y-8 mb-12\">\n          {/* Search Bar */}\n          <div className=\"relative max-w-3xl mx-auto\">\n            <div className=\"relative glass border-0 rounded-3xl overflow-hidden\">\n              <Search className=\"absolute left-6 top-1/2 transform -translate-y-1/2 text-primary h-6 w-6\" />\n              <Input\n                placeholder=\"Search for opportunities, companies, or roles...\"\n                value={filters.search || ''}\n                onChange={(e) => handleFilterChange('search', e.target.value)}\n                className=\"pl-16 pr-6 py-6 text-lg font-medium border-0 bg-transparent placeholder:text-muted-foreground/70 focus:ring-2 focus:ring-primary/50\"\n              />\n            </div>\n          </div>\n\n          {/* Filter Toggle */}\n          <div className=\"flex items-center justify-between max-w-4xl mx-auto\">\n            <Button\n              variant=\"outline\"\n              onClick={() => setShowFilters(!showFilters)}\n              className=\"flex items-center gap-3 rounded-2xl font-semibold px-6 py-3 glass border-0 hover:scale-105 transition-all duration-200\"\n            >\n              <Filter className=\"h-5 w-5\" />\n              {showFilters ? 'Hide Filters' : 'Show Filters'}\n              {hasActiveFilters && (\n                <span className=\"bg-primary text-primary-foreground text-xs px-3 py-1 rounded-full font-bold\">\n                  {Object.values(filters).filter(v => v !== undefined && v !== '').length}\n                </span>\n              )}\n            </Button>\n\n            {hasActiveFilters && (\n              <Button\n                variant=\"ghost\"\n                onClick={clearFilters}\n                className=\"flex items-center gap-2 rounded-2xl font-semibold hover:bg-destructive/10 hover:text-destructive transition-all duration-200\"\n              >\n                <X className=\"h-4 w-4\" />\n                Clear All\n              </Button>\n            )}\n          </div>\n\n          {/* Collapsible Filters */}\n          {showFilters && (\n            <Card className=\"max-w-4xl mx-auto glass border-0 overflow-hidden\">\n              <CardContent className=\"p-8\">\n                <div className=\"mb-6 text-center\">\n                  <h3 className=\"text-xl font-bold text-foreground mb-2\">Refine Your Search 🎯</h3>\n                  <p className=\"text-muted-foreground\">Use filters to find opportunities that match your preferences</p>\n                </div>\n                <div className=\"grid gap-6 md:grid-cols-2 lg:grid-cols-3\">\n                  {/* Location */}\n                  <div>\n                    <label className=\"text-sm font-bold mb-3 block text-foreground\">📍 Location</label>\n                    <Input\n                      placeholder=\"Where do you want to work?\"\n                      value={filters.location || ''}\n                      onChange={(e) => handleFilterChange('location', e.target.value)}\n                      className=\"rounded-2xl border-2 h-12 font-medium\"\n                    />\n                  </div>\n\n                  {/* Category */}\n                  <div>\n                    <label className=\"text-sm font-bold mb-3 block text-foreground\">🏢 Category</label>\n                    <Select value={filters.category || 'all'} onValueChange={(value) => handleFilterChange('category', value)}>\n                      <SelectTrigger className=\"rounded-2xl border-2 h-12 font-medium\">\n                        <SelectValue placeholder=\"What field interests you?\" />\n                      </SelectTrigger>\n                      <SelectContent>\n                        <SelectItem value=\"all\">All Categories</SelectItem>\n                        {categories.map((category) => (\n                          <SelectItem key={category} value={category}>\n                            {category}\n                          </SelectItem>\n                        ))}\n                      </SelectContent>\n                    </Select>\n                  </div>\n\n                  {/* Job Type */}\n                  <div>\n                    <label className=\"text-sm font-bold mb-3 block text-foreground\">⏰ Job Type</label>\n                    <Select value={filters.jobType || 'all'} onValueChange={(value) => handleFilterChange('jobType', value)}>\n                      <SelectTrigger className=\"rounded-2xl border-2 h-12 font-medium\">\n                        <SelectValue placeholder=\"Full-time, part-time?\" />\n                      </SelectTrigger>\n                      <SelectContent>\n                        <SelectItem value=\"all\">All Types</SelectItem>\n                        {jobTypes.map((type) => (\n                          <SelectItem key={type.value} value={type.value}>\n                            {type.label}\n                          </SelectItem>\n                        ))}\n                      </SelectContent>\n                    </Select>\n                  </div>\n\n                  {/* Work Type */}\n                  <div>\n                    <label className=\"text-sm font-bold mb-3 block text-foreground\">🏠 Work Style</label>\n                    <Select value={filters.workLocationType || 'all'} onValueChange={(value) => handleFilterChange('workLocationType', value)}>\n                      <SelectTrigger className=\"rounded-2xl border-2 h-12 font-medium\">\n                        <SelectValue placeholder=\"Remote, hybrid, onsite?\" />\n                      </SelectTrigger>\n                      <SelectContent>\n                        <SelectItem value=\"all\">All Work Types</SelectItem>\n                        {workTypes.map((type) => (\n                          <SelectItem key={type.value} value={type.value}>\n                            {type.label}\n                          </SelectItem>\n                        ))}\n                      </SelectContent>\n                    </Select>\n                  </div>\n\n                  {/* Experience Level */}\n                  <div>\n                    <label className=\"text-sm font-bold mb-3 block text-foreground\">🎓 Experience Level</label>\n                    <Select value={filters.experienceLevel || 'all'} onValueChange={(value) => handleFilterChange('experienceLevel', value)}>\n                      <SelectTrigger className=\"rounded-2xl border-2 h-12 font-medium\">\n                        <SelectValue placeholder=\"How experienced are you?\" />\n                      </SelectTrigger>\n                      <SelectContent>\n                        <SelectItem value=\"all\">All Levels</SelectItem>\n                        {experienceLevels.map((level) => (\n                          <SelectItem key={level.value} value={level.value}>\n                            {level.label}\n                          </SelectItem>\n                        ))}\n                      </SelectContent>\n                    </Select>\n                  </div>\n\n                  {/* Salary Range */}\n                  <div className=\"md:col-span-2 lg:col-span-1\">\n                    <label className=\"text-sm font-bold mb-3 block text-foreground\">💰 Salary Range (USD)</label>\n                    <div className=\"flex gap-3\">\n                      <Input\n                        type=\"number\"\n                        placeholder=\"Min salary\"\n                        value={filters.salaryMin || ''}\n                        onChange={(e) => handleFilterChange('salaryMin', e.target.value ? parseInt(e.target.value) : undefined)}\n                        className=\"rounded-2xl border-2 h-12 font-medium\"\n                      />\n                      <Input\n                        type=\"number\"\n                        placeholder=\"Max salary\"\n                        value={filters.salaryMax || ''}\n                        onChange={(e) => handleFilterChange('salaryMax', e.target.value ? parseInt(e.target.value) : undefined)}\n                        className=\"rounded-2xl border-2 h-12 font-medium\"\n                      />\n                    </div>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          )}\n        </div>\n\n        {/* Results */}\n        {isLoading ? (\n          <div className=\"grid gap-8 md:grid-cols-2 xl:grid-cols-3\">\n            {Array.from({ length: 6 }).map((_, i) => (\n              <JobCardSkeleton key={i} />\n            ))}\n          </div>\n        ) : error ? (\n          <div className=\"text-center py-20\">\n            <div className=\"mb-6\">\n              <div className=\"mx-auto w-20 h-20 rounded-3xl bg-destructive/10 flex items-center justify-center mb-4\">\n                <X className=\"h-10 w-10 text-destructive\" />\n              </div>\n              <h3 className=\"text-2xl font-bold text-foreground mb-2\">Oops! Something went wrong</h3>\n              <p className=\"text-muted-foreground text-lg mb-6\">Failed to load jobs. Please try again.</p>\n            </div>\n            <Button onClick={() => window.location.reload()} className=\"rounded-2xl font-bold px-8 py-3\">\n              Try Again\n            </Button>\n          </div>\n        ) : !data?.jobs?.length ? (\n          <div className=\"text-center py-20\">\n            <div className=\"mb-6\">\n              <div className=\"mx-auto w-20 h-20 rounded-3xl bg-muted/50 flex items-center justify-center mb-4\">\n                <Search className=\"h-10 w-10 text-muted-foreground\" />\n              </div>\n              <h3 className=\"text-2xl font-bold text-foreground mb-2\">No jobs found</h3>\n              <p className=\"text-muted-foreground text-lg mb-6\">Try adjusting your filters or search terms.</p>\n            </div>\n            <Button variant=\"outline\" onClick={clearFilters} className=\"rounded-2xl font-bold px-8 py-3\">\n              Clear Filters\n            </Button>\n          </div>\n        ) : (\n          <>\n            <div className=\"mb-8\">\n              <div className=\"flex items-center justify-between mb-6\">\n                <h2 className=\"text-2xl font-bold text-foreground\">\n                  {data.pagination.total} opportunities found 🎯\n                </h2>\n                <span className=\"text-muted-foreground font-medium\">\n                  Page {currentPage} of {data.pagination.totalPages}\n                </span>\n              </div>\n            </div>\n            <div className=\"grid gap-8 md:grid-cols-2 xl:grid-cols-3\">\n              {data.jobs.map((job) => (\n                <JobCard key={job.id} job={job} />\n              ))}\n            </div>\n\n            {/* Pagination */}\n            {data.pagination.totalPages > 1 && (\n              <div className=\"mt-16 flex items-center justify-center\">\n                <div className=\"glass rounded-3xl p-2 flex items-center space-x-2\">\n                  <Button\n                    variant=\"ghost\"\n                    onClick={() => handlePageChange(currentPage - 1)}\n                    disabled={!data.pagination.hasPrev}\n                    className=\"rounded-2xl font-semibold px-6 py-3 disabled:opacity-50\"\n                  >\n                    Previous\n                  </Button>\n\n                  <div className=\"flex items-center space-x-1\">\n                    {Array.from({ length: Math.min(5, data.pagination.totalPages) }, (_, i) => {\n                      const page = i + 1\n                      return (\n                        <Button\n                          key={page}\n                          variant={currentPage === page ? \"default\" : \"ghost\"}\n                          size=\"sm\"\n                          onClick={() => handlePageChange(page)}\n                          className={`rounded-2xl font-bold w-12 h-12 ${\n                            currentPage === page\n                              ? 'gradient-primary text-primary-foreground'\n                              : 'hover:bg-muted/50'\n                          }`}\n                        >\n                          {page}\n                        </Button>\n                      )\n                    })}\n                  </div>\n\n                  <Button\n                    variant=\"ghost\"\n                    onClick={() => handlePageChange(currentPage + 1)}\n                    disabled={!data.pagination.hasNext}\n                    className=\"rounded-2xl font-semibold px-6 py-3 disabled:opacity-50\"\n                  >\n                    Next\n                  </Button>\n                </div>\n              </div>\n            )}\n          </>\n        )}\n      </div>\n    </div>\n  )\n}\n\nexport default function JobsPage() {\n  return (\n    <Suspense fallback={\n      <div className=\"min-h-screen bg-background flex items-center justify-center\">\n        <div className=\"flex items-center\">\n          <Loader2 className=\"h-8 w-8 animate-spin text-muted-foreground\" />\n          <span className=\"ml-3 text-muted-foreground\">Loading jobs...</span>\n        </div>\n      </div>\n    }>\n      <JobsContent />\n    </Suspense>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;AAZA;;;;;;;;;;;;AAcA,SAAS;;IACP,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;QACjD,QAAQ,aAAa,GAAG,CAAC,aAAa;QACtC,UAAU,aAAa,GAAG,CAAC,eAAe;QAC1C,UAAU,aAAa,GAAG,CAAC,eAAe;QAC1C,kBAAkB,aAAa,GAAG,CAAC,uBAAuB;QAC1D,SAAS,aAAa,GAAG,CAAC,cAAc;QACxC,iBAAiB,aAAa,GAAG,CAAC,sBAAsB;QACxD,WAAW,aAAa,GAAG,CAAC,eAAe,SAAS,aAAa,GAAG,CAAC,gBAAiB;QACtF,WAAW,aAAa,GAAG,CAAC,eAAe,SAAS,aAAa,GAAG,CAAC,gBAAiB;IACxF;IACA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QAC1C,UAAU;YAAC;YAAQ;YAAS;SAAY;QACxC,OAAO;oCAAE,IAAM,oHAAA,CAAA,MAAG,CAAC,OAAO,CAAC,SAAS,aAAa;;IACnD;IAEA,IAAG,CAAC,WAAU;QACZ,QAAQ,GAAG,CAAC;IACd;IAEA,MAAM,EAAE,MAAM,cAAc,EAAE,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACxC,UAAU;YAAC;SAAiB;QAC5B,OAAO;oCAAE,IAAM,oHAAA,CAAA,MAAG,CAAC,gBAAgB;;IACrC;IAEA,MAAM,aAAa,gBAAgB,cAAc,EAAE;IAEnD,MAAM,WAAW;QACf;YAAE,OAAO;YAAa,OAAO;QAAY;QACzC;YAAE,OAAO;YAAa,OAAO;QAAY;QACzC;YAAE,OAAO;YAAY,OAAO;QAAW;QACvC;YAAE,OAAO;YAAc,OAAO;QAAa;KAC5C;IAED,MAAM,YAAY;QAChB;YAAE,OAAO;YAAU,OAAO;QAAU;QACpC;YAAE,OAAO;YAAU,OAAO;QAAS;QACnC;YAAE,OAAO;YAAU,OAAO;QAAS;KACpC;IAED,MAAM,mBAAmB;QACvB;YAAE,OAAO;YAAW,OAAO;QAA+B;QAC1D;YAAE,OAAO;YAAW,OAAO;QAAU;QACrC;YAAE,OAAO;YAAmB,OAAO;QAA6B;QAChE;YAAE,OAAO;YAAoB,OAAO;QAAW;QAC/C;YAAE,OAAO;YAAsB,OAAO;QAAY;QAClD;YAAE,OAAO;YAAuB,OAAO;QAAY;QACnD;YAAE,OAAO;YAAmB,OAAO;QAAW;KAC/C;IAED,MAAM,qBAAqB,CAAC,KAAuB;QACjD,WAAW,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,IAAI,EAAE,UAAU,MAAM,UAAU,QAAQ,YAAY;YAAM,CAAC;QAC3F,eAAe;IACjB;IAEA,MAAM,eAAe;QACnB,WAAW,CAAC;QACZ,eAAe;IACjB;IAEA,MAAM,mBAAmB,OAAO,MAAM,CAAC,SAAS,IAAI,CAAC,CAAA,QAAS,UAAU,aAAa,UAAU;IAE/F,MAAM,mBAAmB,CAAC;QACxB,eAAe;QACf,OAAO,QAAQ,CAAC;YAAE,KAAK;YAAG,UAAU;QAAS;IAC/C;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAQ,WAAU;;kCAEjB,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;wBAAiF,OAAO;4BAAC,gBAAgB;wBAAI;;;;;;kCAE5H,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;;4CAA4F;4CACtG,OAAO,GAAG,KAAK,UAAU,CAAC,KAAK,CAAC,wBAAwB,CAAC,GAAG;;;;;;;;;;;;8CAGpE,6LAAC;oCAAG,WAAU;;wCAAgF;wCAC/E;sDACb,6LAAC;4CAAK,WAAU;sDAAiD;;;;;;;;;;;;8CAInE,6LAAC;oCAAE,WAAU;;wCAAoF;sDAE/F,6LAAC;4CAAK,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMxD,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC,oIAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,OAAO,QAAQ,MAAM,IAAI;4CACzB,UAAU,CAAC,IAAM,mBAAmB,UAAU,EAAE,MAAM,CAAC,KAAK;4CAC5D,WAAU;;;;;;;;;;;;;;;;;0CAMhB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,SAAS,IAAM,eAAe,CAAC;wCAC/B,WAAU;;0DAEV,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CACjB,cAAc,iBAAiB;4CAC/B,kCACC,6LAAC;gDAAK,WAAU;0DACb,OAAO,MAAM,CAAC,SAAS,MAAM,CAAC,CAAA,IAAK,MAAM,aAAa,MAAM,IAAI,MAAM;;;;;;;;;;;;oCAK5E,kCACC,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,SAAS;wCACT,WAAU;;0DAEV,6LAAC,+LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;4BAO9B,6BACC,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAyC;;;;;;8DACvD,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,6LAAC;4CAAI,WAAU;;8DAEb,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAChE,6LAAC,oIAAA,CAAA,QAAK;4DACJ,aAAY;4DACZ,OAAO,QAAQ,QAAQ,IAAI;4DAC3B,UAAU,CAAC,IAAM,mBAAmB,YAAY,EAAE,MAAM,CAAC,KAAK;4DAC9D,WAAU;;;;;;;;;;;;8DAKd,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAChE,6LAAC,qIAAA,CAAA,SAAM;4DAAC,OAAO,QAAQ,QAAQ,IAAI;4DAAO,eAAe,CAAC,QAAU,mBAAmB,YAAY;;8EACjG,6LAAC,qIAAA,CAAA,gBAAa;oEAAC,WAAU;8EACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;wEAAC,aAAY;;;;;;;;;;;8EAE3B,6LAAC,qIAAA,CAAA,gBAAa;;sFACZ,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAM;;;;;;wEACvB,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC,qIAAA,CAAA,aAAU;gFAAgB,OAAO;0FAC/B;+EADc;;;;;;;;;;;;;;;;;;;;;;;8DASzB,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAChE,6LAAC,qIAAA,CAAA,SAAM;4DAAC,OAAO,QAAQ,OAAO,IAAI;4DAAO,eAAe,CAAC,QAAU,mBAAmB,WAAW;;8EAC/F,6LAAC,qIAAA,CAAA,gBAAa;oEAAC,WAAU;8EACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;wEAAC,aAAY;;;;;;;;;;;8EAE3B,6LAAC,qIAAA,CAAA,gBAAa;;sFACZ,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAM;;;;;;wEACvB,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,qIAAA,CAAA,aAAU;gFAAkB,OAAO,KAAK,KAAK;0FAC3C,KAAK,KAAK;+EADI,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;8DASnC,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAChE,6LAAC,qIAAA,CAAA,SAAM;4DAAC,OAAO,QAAQ,gBAAgB,IAAI;4DAAO,eAAe,CAAC,QAAU,mBAAmB,oBAAoB;;8EACjH,6LAAC,qIAAA,CAAA,gBAAa;oEAAC,WAAU;8EACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;wEAAC,aAAY;;;;;;;;;;;8EAE3B,6LAAC,qIAAA,CAAA,gBAAa;;sFACZ,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAM;;;;;;wEACvB,UAAU,GAAG,CAAC,CAAC,qBACd,6LAAC,qIAAA,CAAA,aAAU;gFAAkB,OAAO,KAAK,KAAK;0FAC3C,KAAK,KAAK;+EADI,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;8DASnC,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAChE,6LAAC,qIAAA,CAAA,SAAM;4DAAC,OAAO,QAAQ,eAAe,IAAI;4DAAO,eAAe,CAAC,QAAU,mBAAmB,mBAAmB;;8EAC/G,6LAAC,qIAAA,CAAA,gBAAa;oEAAC,WAAU;8EACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;wEAAC,aAAY;;;;;;;;;;;8EAE3B,6LAAC,qIAAA,CAAA,gBAAa;;sFACZ,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAM;;;;;;wEACvB,iBAAiB,GAAG,CAAC,CAAC,sBACrB,6LAAC,qIAAA,CAAA,aAAU;gFAAmB,OAAO,MAAM,KAAK;0FAC7C,MAAM,KAAK;+EADG,MAAM,KAAK;;;;;;;;;;;;;;;;;;;;;;;8DASpC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAChE,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,oIAAA,CAAA,QAAK;oEACJ,MAAK;oEACL,aAAY;oEACZ,OAAO,QAAQ,SAAS,IAAI;oEAC5B,UAAU,CAAC,IAAM,mBAAmB,aAAa,EAAE,MAAM,CAAC,KAAK,GAAG,SAAS,EAAE,MAAM,CAAC,KAAK,IAAI;oEAC7F,WAAU;;;;;;8EAEZ,6LAAC,oIAAA,CAAA,QAAK;oEACJ,MAAK;oEACL,aAAY;oEACZ,OAAO,QAAQ,SAAS,IAAI;oEAC5B,UAAU,CAAC,IAAM,mBAAmB,aAAa,EAAE,MAAM,CAAC,KAAK,GAAG,SAAS,EAAE,MAAM,CAAC,KAAK,IAAI;oEAC7F,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAWzB,0BACC,6LAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,6LAAC,gIAAA,CAAA,kBAAe,MAAM;;;;;;;;;+BAGxB,sBACF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;kDAEf,6LAAC;wCAAG,WAAU;kDAA0C;;;;;;kDACxD,6LAAC;wCAAE,WAAU;kDAAqC;;;;;;;;;;;;0CAEpD,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;gCAAI,WAAU;0CAAkC;;;;;;;;;;;+BAI7F,CAAC,MAAM,MAAM,uBACf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;kDAEpB,6LAAC;wCAAG,WAAU;kDAA0C;;;;;;kDACxD,6LAAC;wCAAE,WAAU;kDAAqC;;;;;;;;;;;;0CAEpD,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS;gCAAc,WAAU;0CAAkC;;;;;;;;;;;6CAK/F;;0CACE,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;gDACX,KAAK,UAAU,CAAC,KAAK;gDAAC;;;;;;;sDAEzB,6LAAC;4CAAK,WAAU;;gDAAoC;gDAC5C;gDAAY;gDAAK,KAAK,UAAU,CAAC,UAAU;;;;;;;;;;;;;;;;;;0CAIvD,6LAAC;gCAAI,WAAU;0CACZ,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,oBACd,6LAAC,4IAAA,CAAA,UAAO;wCAAc,KAAK;uCAAb,IAAI,EAAE;;;;;;;;;;4BAKvB,KAAK,UAAU,CAAC,UAAU,GAAG,mBAC5B,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,SAAS,IAAM,iBAAiB,cAAc;4CAC9C,UAAU,CAAC,KAAK,UAAU,CAAC,OAAO;4CAClC,WAAU;sDACX;;;;;;sDAID,6LAAC;4CAAI,WAAU;sDACZ,MAAM,IAAI,CAAC;gDAAE,QAAQ,KAAK,GAAG,CAAC,GAAG,KAAK,UAAU,CAAC,UAAU;4CAAE,GAAG,CAAC,GAAG;gDACnE,MAAM,OAAO,IAAI;gDACjB,qBACE,6LAAC,qIAAA,CAAA,SAAM;oDAEL,SAAS,gBAAgB,OAAO,YAAY;oDAC5C,MAAK;oDACL,SAAS,IAAM,iBAAiB;oDAChC,WAAW,CAAC,gCAAgC,EAC1C,gBAAgB,OACZ,6CACA,qBACJ;8DAED;mDAVI;;;;;4CAaX;;;;;;sDAGF,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,SAAS,IAAM,iBAAiB,cAAc;4CAC9C,UAAU,CAAC,KAAK,UAAU,CAAC,OAAO;4CAClC,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB;GA5WS;;QACc,qIAAA,CAAA,kBAAe;QAcD,8KAAA,CAAA,WAAQ;QASV,8KAAA,CAAA,WAAQ;;;KAxBlC;AA8WM,SAAS;IACtB,qBACE,6LAAC,6JAAA,CAAA,WAAQ;QAAC,wBACR,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,6LAAC;wBAAK,WAAU;kCAA6B;;;;;;;;;;;;;;;;;kBAIjD,cAAA,6LAAC;;;;;;;;;;AAGP;MAbwB", "debugId": null}}]}