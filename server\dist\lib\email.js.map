{"version": 3, "file": "email.js", "sourceRoot": "", "sources": ["../../src/lib/email.ts"], "names": [], "mappings": ";;;;;;AA8MA,0CAiBC;AA/ND,4DAAoC;AACpC,oDAA4B;AAG5B,MAAM,WAAW,GAAG;IAClB,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,gBAAgB;IAC/C,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,KAAK,CAAC;IAC9C,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,KAAK,MAAM;IAC1C,IAAI,EAAE;QACJ,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,EAAE;QACjC,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,EAAE;KAClC;IACD,GAAG,EAAE;QACH,kBAAkB,EAAE,KAAK;KAC1B;CACF,CAAC;AAGF,MAAM,WAAW,GAAG,oBAAU,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;AAGrD,MAAM,iBAAiB,GAAG,KAAK,IAAsB,EAAE;IAC5D,IAAI,CAAC;QACH,MAAM,WAAW,CAAC,MAAM,EAAE,CAAC;QAC3B,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;QACxC,OAAO,IAAI,CAAC;IACd,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;QAC7D,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC,CAAC;AATW,QAAA,iBAAiB,qBAS5B;AAGK,MAAM,aAAa,GAAG,GAAW,EAAE;IACxC,OAAO,gBAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AAChD,CAAC,CAAC;AAFW,QAAA,aAAa,iBAExB;AAGF,MAAM,gBAAgB,GAAG,CAAC,IAAsC,EAAE,IAAS,EAAqC,EAAE;IAChH,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,uBAAuB,CAAC;IAEpE,IAAI,IAAI,KAAK,cAAc,EAAE,CAAC;QAC5B,OAAO;YACL,OAAO,EAAE,kCAAkC;YAC3C,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BA4Be,OAAO,4BAA4B,IAAI,CAAC,KAAK;;;;;;kEAMN,OAAO,4BAA4B,IAAI,CAAC,KAAK;;;;;;;;;;;;;OAaxG;SACF,CAAC;IACJ,CAAC;SAAM,CAAC;QACN,OAAO;YACL,OAAO,EAAE,kCAAkC;YAC3C,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BA6Be,OAAO,8BAA8B,IAAI,CAAC,KAAK;;;;;;;;;;;;;kEAaR,OAAO,8BAA8B,IAAI,CAAC,KAAK;;;;;;;;;;;;;;OAc1G;SACF,CAAC;IACJ,CAAC;AACH,CAAC,CAAC;AAGK,MAAM,qBAAqB,GAAG,KAAK,EAAE,KAAa,EAAE,KAAa,EAAoB,EAAE;IAC5F,IAAI,CAAC;QACH,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,gBAAgB,CAAC,cAAc,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;QAEtE,MAAM,WAAW,CAAC,QAAQ,CAAC;YACzB,IAAI,EAAE;gBACJ,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,gBAAgB;gBAC/C,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,EAAE;aAC/D;YACD,EAAE,EAAE,KAAK;YACT,OAAO;YACP,IAAI;SACL,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,gCAAgC,KAAK,EAAE,CAAC,CAAC;QACrD,OAAO,CAAC,GAAG,CAAC,0BAA0B,KAAK,EAAE,CAAC,CAAC;QAC/C,OAAO,IAAI,CAAC;IACd,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;QAC7D,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC,CAAC;AArBW,QAAA,qBAAqB,yBAqBhC;AAGK,MAAM,sBAAsB,GAAG,KAAK,EAAE,KAAa,EAAE,KAAa,EAAoB,EAAE;IAC7F,IAAI,CAAC;QACH,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,gBAAgB,CAAC,eAAe,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;QAEvE,MAAM,WAAW,CAAC,QAAQ,CAAC;YACzB,IAAI,EAAE;gBACJ,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,gBAAgB;gBAC/C,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,EAAE;aAC/D;YACD,EAAE,EAAE,KAAK;YACT,OAAO;YACP,IAAI;SACL,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,kCAAkC,KAAK,EAAE,CAAC,CAAC;QACvD,OAAO,CAAC,GAAG,CAAC,mBAAmB,KAAK,EAAE,CAAC,CAAC;QACxC,OAAO,IAAI,CAAC;IACd,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;QAC/D,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC,CAAA;AArBY,QAAA,sBAAsB,0BAqBlC;AAGM,KAAK,UAAU,eAAe;IACnC,IAAI,CAAC;QACH,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;QACjD,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACpD,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACpD,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACpD,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACtD,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAGpD,MAAM,WAAW,CAAC,MAAM,EAAE,CAAC;QAC3B,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;QAC/C,OAAO,IAAI,CAAC;IACd,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;QAC3D,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAAA,CAAC"}