import { z } from 'zod';
export declare const adminJobFiltersSchema: z.ZodObject<{
    page: z.ZodDefault<z.ZodEffects<z.ZodUnion<[z.ZodString, z.ZodNumber]>, number, string | number>>;
    limit: z.<PERSON>od<PERSON>efault<z.ZodEffects<z.ZodUnion<[z.ZodString, z.ZodNumber]>, number, string | number>>;
    search: z.ZodOptional<z.ZodString>;
    category: z.ZodOptional<z.ZodString>;
    isActive: z.ZodOptional<z.ZodEffects<z.ZodUnion<[z.ZodString, z.ZodBoolean]>, boolean, string | boolean>>;
}, "strip", z.ZodTypeAny, {
    page: number;
    limit: number;
    search?: string | undefined;
    category?: string | undefined;
    isActive?: boolean | undefined;
}, {
    search?: string | undefined;
    category?: string | undefined;
    isActive?: string | boolean | undefined;
    page?: string | number | undefined;
    limit?: string | number | undefined;
}>;
export declare const adminUserFiltersSchema: z.ZodObject<{
    page: z.ZodDefault<z.ZodEffects<z.ZodUnion<[z.ZodString, z.ZodNumber]>, number, string | number>>;
    limit: z.ZodDefault<z.ZodEffects<z.ZodUnion<[z.ZodString, z.ZodNumber]>, number, string | number>>;
    search: z.ZodOptional<z.ZodString>;
    experienceLevel: z.ZodOptional<z.ZodEnum<["FRESHER", "ONE_TO_THREE_YEARS", "THREE_PLUS_YEARS"]>>;
}, "strip", z.ZodTypeAny, {
    page: number;
    limit: number;
    search?: string | undefined;
    experienceLevel?: "FRESHER" | "ONE_TO_THREE_YEARS" | "THREE_PLUS_YEARS" | undefined;
}, {
    search?: string | undefined;
    experienceLevel?: "FRESHER" | "ONE_TO_THREE_YEARS" | "THREE_PLUS_YEARS" | undefined;
    page?: string | number | undefined;
    limit?: string | number | undefined;
}>;
export declare const adminApplicationFiltersSchema: z.ZodObject<{
    page: z.ZodDefault<z.ZodEffects<z.ZodUnion<[z.ZodString, z.ZodNumber]>, number, string | number>>;
    limit: z.ZodDefault<z.ZodEffects<z.ZodUnion<[z.ZodString, z.ZodNumber]>, number, string | number>>;
    jobId: z.ZodOptional<z.ZodString>;
    status: z.ZodOptional<z.ZodEnum<["PENDING", "REVIEWED", "SHORTLISTED", "ACCEPTED", "REJECTED", "HIRED"]>>;
}, "strip", z.ZodTypeAny, {
    page: number;
    limit: number;
    status?: "PENDING" | "REVIEWED" | "SHORTLISTED" | "ACCEPTED" | "REJECTED" | "HIRED" | undefined;
    jobId?: string | undefined;
}, {
    status?: "PENDING" | "REVIEWED" | "SHORTLISTED" | "ACCEPTED" | "REJECTED" | "HIRED" | undefined;
    page?: string | number | undefined;
    limit?: string | number | undefined;
    jobId?: string | undefined;
}>;
export declare const adminBlogFiltersSchema: z.ZodObject<{
    page: z.ZodDefault<z.ZodEffects<z.ZodUnion<[z.ZodString, z.ZodNumber]>, number, string | number>>;
    limit: z.ZodDefault<z.ZodEffects<z.ZodUnion<[z.ZodString, z.ZodNumber]>, number, string | number>>;
    search: z.ZodOptional<z.ZodString>;
    isPublished: z.ZodOptional<z.ZodEffects<z.ZodUnion<[z.ZodString, z.ZodBoolean]>, boolean, string | boolean>>;
}, "strip", z.ZodTypeAny, {
    page: number;
    limit: number;
    search?: string | undefined;
    isPublished?: boolean | undefined;
}, {
    search?: string | undefined;
    page?: string | number | undefined;
    limit?: string | number | undefined;
    isPublished?: string | boolean | undefined;
}>;
export declare const updateApplicationStatusSchema: z.ZodObject<{
    status: z.ZodEnum<["PENDING", "REVIEWED", "SHORTLISTED", "ACCEPTED", "REJECTED", "HIRED"]>;
}, "strip", z.ZodTypeAny, {
    status: "PENDING" | "REVIEWED" | "SHORTLISTED" | "ACCEPTED" | "REJECTED" | "HIRED";
}, {
    status: "PENDING" | "REVIEWED" | "SHORTLISTED" | "ACCEPTED" | "REJECTED" | "HIRED";
}>;
export type AdminJobFilters = z.infer<typeof adminJobFiltersSchema>;
export type AdminUserFilters = z.infer<typeof adminUserFiltersSchema>;
export type AdminApplicationFilters = z.infer<typeof adminApplicationFiltersSchema>;
export type AdminBlogFilters = z.infer<typeof adminBlogFiltersSchema>;
export type UpdateApplicationStatusInput = z.infer<typeof updateApplicationStatusSchema>;
//# sourceMappingURL=admin.d.ts.map