{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/job-portal/client/src/lib/api.ts"], "sourcesContent": ["const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api'\n\n// Types\nexport interface User {\n  id: string\n  email: string\n  phone?: string\n  experienceLevel?: 'STUDENT' | 'FRESHER' | 'INTERNSHIP_ONLY' | 'ZERO_TO_ONE_YEAR' | 'ONE_TO_THREE_YEARS' | 'THREE_TO_FIVE_YEARS' | 'FIVE_PLUS_YEARS' | 'THREE_PLUS_YEARS'\n  profileCompleted: boolean\n  // Flat structure from API\n  firstName?: string\n  lastName?: string\n  bio?: string\n  skills?: string[]\n  experience?: string\n  education?: string\n  location?: string\n  website?: string\n  linkedin?: string\n  github?: string\n  profilePicture?: string\n  resume?: string\n  // Legacy nested structure for backward compatibility\n  profile?: UserProfile\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface UserProfile {\n  id?: string\n  userId?: string\n  firstName?: string\n  lastName?: string\n  phone?: string\n  bio?: string\n  skills?: string[]\n  experience?: string\n  education?: string\n  location?: string\n  website?: string\n  linkedin?: string\n  github?: string\n  resume?: string\n  profilePicture?: string\n  resumeUrl?: string\n  profilePictureUrl?: string\n  createdAt?: string\n  updatedAt?: string\n}\n\nexport interface Job {\n  id: string\n  title: string\n  slug: string\n  description: string\n  requirements?: string[]\n  responsibilities?: string[]\n  category: string\n  location: string\n  workLocationType?: 'ONSITE' | 'REMOTE' | 'HYBRID'\n  jobType: 'FULL_TIME' | 'PART_TIME' | 'CONTRACT' | 'INTERNSHIP'\n  experienceLevel: 'STUDENT' | 'FRESHER' | 'INTERNSHIP_ONLY' | 'ZERO_TO_ONE_YEAR' | 'ONE_TO_THREE_YEARS' | 'THREE_TO_FIVE_YEARS' | 'FIVE_PLUS_YEARS' | 'THREE_PLUS_YEARS'\n  salaryMin?: number\n  salaryMax?: number\n  salaryNegotiable?: boolean\n  currency?: string\n  companyName: string\n  companyLogo?: string\n  isFeatured?: boolean\n  isActive?: boolean\n  createdAt: string\n  updatedAt?: string\n}\n\nexport interface BlogPost {\n  id: string\n  title: string\n  slug: string\n  content?: string\n  excerpt?: string\n  featuredImage?: string\n  author?: string\n  published?: boolean\n  publishedAt?: string\n  createdAt: string\n  updatedAt?: string\n}\n\nexport interface JobApplication {\n  id: string\n  jobId: string\n  userId: string\n  status: 'PENDING' | 'REVIEWED' | 'SHORTLISTED' | 'ACCEPTED' | 'REJECTED' | 'HIRED'\n  message?: string\n  coverLetter?: string // For backward compatibility\n  job: Job\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface AuthResponse {\n  user: User\n  token: string\n}\n\nexport interface PaginationInfo {\n  page: number\n  limit: number\n  total: number\n  totalPages: number\n  hasNext: boolean\n  hasPrev: boolean\n}\n\nexport interface JobsResponse {\n  jobs: Job[]\n  pagination: PaginationInfo\n}\n\nexport interface BlogsResponse {\n  blogs: BlogPost[]\n  pagination: PaginationInfo\n}\n\nexport interface JobFilters {\n  search?: string\n  category?: string\n  location?: string\n  workLocationType?: string\n  jobType?: string\n  experienceLevel?: string\n  salaryMin?: number\n  salaryMax?: number\n}\n\n// API Client Class\nclass ApiClient {\n  private baseURL: string\n  private token: string | null = null\n\n  constructor(baseURL: string) {\n    this.baseURL = baseURL\n    // Initialize token from localStorage if available\n    if (typeof window !== 'undefined') {\n      this.token = localStorage.getItem('auth_token')\n    }\n  }\n\n  setToken(token: string) {\n    this.token = token\n    if (typeof window !== 'undefined') {\n      localStorage.setItem('auth_token', token)\n    }\n  }\n\n  clearToken() {\n    this.token = null\n    if (typeof window !== 'undefined') {\n      localStorage.removeItem('auth_token')\n    }\n  }\n\n  private async request<T>(\n    endpoint: string,\n    options: RequestInit = {}\n  ): Promise<T> {\n    const url = `${this.baseURL}${endpoint}`\n    const headers: Record<string, string> = {\n      'Content-Type': 'application/json',\n      ...(options.headers as Record<string, string>),\n    }\n\n    if (this.token) {\n      headers.Authorization = `Bearer ${this.token}`\n    }\n\n    try {\n      const response = await fetch(url, {\n        ...options,\n        headers,\n      })\n\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({\n          message: response.status === 404 ? 'Resource not found' : 'Network error',\n          status: response.status\n        }))\n\n        const error = new Error(errorData.message || `HTTP ${response.status}`)\n        ;(error as any).status = response.status\n        ;(error as any).code = errorData.code\n        throw error\n      }\n\n      return response.json()\n    } catch (error) {\n      // Handle network errors\n      if (error instanceof TypeError || !navigator.onLine) {\n        const networkError = new Error('Network error. Please check your internet connection.')\n        ;(networkError as any).status = 0\n        throw networkError\n      }\n      throw error\n    }\n  }\n\n  // Auth endpoints\n  async login(email: string, password: string): Promise<AuthResponse> {\n    return this.request<AuthResponse>('/auth/login', {\n      method: 'POST',\n      body: JSON.stringify({ email, password }),\n    })\n  }\n\n  async register(data: {\n    fullName: string\n    email: string\n    password: string\n    phone?: string\n    experienceLevel?: string\n  }): Promise<AuthResponse> {\n    return this.request<AuthResponse>('/auth/register', {\n      method: 'POST',\n      body: JSON.stringify(data),\n    })\n  }\n\n  async getProfile(): Promise<User> {\n    const response = await this.request<{ user: User }>('/auth/me')\n    return response.user\n  }\n\n  async updateProfile(data: Partial<UserProfile>): Promise<{ user: User; message: string }> {\n    return this.request<{ user: User; message: string }>('/user/profile', {\n      method: 'PUT',\n      body: JSON.stringify(data),\n    })\n  }\n\n  // Jobs endpoints\n  async getJobs(\n    filters: JobFilters = {},\n    page = 1,\n    limit = 12\n  ): Promise<JobsResponse> {\n    const params = new URLSearchParams({\n      page: page.toString(),\n      limit: limit.toString(),\n      ...Object.fromEntries(\n        Object.entries(filters).filter(([, value]) => value !== undefined && value !== '')\n      ),\n    })\n\n    return this.request<JobsResponse>(`/jobs?${params}`)\n  }\n\n  async getJob(slug: string): Promise<{ job: Job; hasApplied: boolean }> {\n    return this.request<{ job: Job; hasApplied: boolean }>(`/jobs/${slug}`)\n  }\n\n  async getFeaturedJobs(): Promise<Job[]> {\n    try {\n      const result = await this.request<{ jobs: Job[] }>('/jobs/featured')\n      return Array.isArray(result.jobs) ? result.jobs : []\n    } catch (error) {\n      console.warn('Failed to fetch featured jobs:', error)\n      return []\n    }\n  }\n\n  async applyToJob(jobId: string, coverLetter?: string): Promise<{ application: JobApplication; message: string }> {\n    return this.request<{ application: JobApplication; message: string }>(`/jobs/${jobId}/apply`, {\n      method: 'POST',\n      body: JSON.stringify({ message: coverLetter }),\n    })\n  }\n\n  async getMyApplications(): Promise<{ applications: JobApplication[]; pagination: PaginationInfo }> {\n    return this.request<{ applications: JobApplication[]; pagination: PaginationInfo }>('/user/applications')\n  }\n\n  async getJobCategories(): Promise<{ categories: string[]; total: number }> {\n    return this.request<{ categories: string[]; total: number }>('/jobs/categories')\n  }\n\n  // Blog endpoints\n  async getBlogs(page = 1, limit = 10): Promise<BlogsResponse> {\n    const params = new URLSearchParams({\n      page: page.toString(),\n      limit: limit.toString(),\n    })\n\n    return this.request<BlogsResponse>(`/blogs?${params}`)\n  }\n\n  async getBlog(slug: string): Promise<BlogPost> {\n    return this.request<BlogPost>(`/blogs/${slug}`)\n  }\n\n  // File upload\n  async uploadFile(file: File, type: 'resume' | 'profile-picture'): Promise<{ url: string }> {\n    const formData = new FormData()\n    formData.append('file', file)\n\n    const headers: Record<string, string> = {}\n    if (this.token) {\n      headers.Authorization = `Bearer ${this.token}`\n    }\n\n    // Use specific endpoints based on type\n    const endpoint = type === 'resume' ? '/upload/resume' : '/upload/profile-picture'\n\n    const response = await fetch(`${this.baseURL}${endpoint}`, {\n      method: 'POST',\n      headers,\n      body: formData,\n    })\n\n    if (!response.ok) {\n      const error = await response.json().catch(() => ({ message: 'Upload failed' }))\n      throw new Error(error.message || `HTTP ${response.status}`)\n    }\n\n    const result = await response.json()\n\n    // Normalize response format - backend returns different field names\n    if (type === 'resume') {\n      return { url: result.resume }\n    } else {\n      return { url: result.profilePicture }\n    }\n  }\n\n  // Contact form\n  async submitContactForm(data: {\n    name: string\n    email: string\n    subject: string\n    message: string\n  }): Promise<{ success: boolean; message: string }> {\n    return this.request<{ success: boolean; message: string }>('/contact', {\n      method: 'POST',\n      body: JSON.stringify(data),\n    })\n  }\n\n  // Email verification\n  async sendVerificationEmail(email: string): Promise<{ message: string; emailSent: boolean }> {\n    return this.request<{ message: string; emailSent: boolean }>('/auth/send-verification', {\n      method: 'POST',\n      body: JSON.stringify({ email }),\n    })\n  }\n\n  async resendVerificationEmail(email: string): Promise<{ message: string; emailSent: boolean }> {\n    return this.sendVerificationEmail(email)\n  }\n\n  async verifyEmail(token: string): Promise<{ message: string; user: any; token: string }> {\n    return this.request<{ message: string; user: any; token: string }>('/auth/verify-email', {\n      method: 'POST',\n      body: JSON.stringify({ token }),\n    })\n  }\n\n  // Password reset\n  async forgotPassword(email: string): Promise<{ message: string; emailSent?: boolean }> {\n    return this.request<{ message: string; emailSent?: boolean }>('/auth/forgot-password', {\n      method: 'POST',\n      body: JSON.stringify({ email }),\n    })\n  }\n\n  async resetPassword(token: string, password: string): Promise<{ message: string; user: any; token: string }> {\n    return this.request<{ message: string; user: any; token: string }>('/auth/reset-password', {\n      method: 'POST',\n      body: JSON.stringify({ token, password }),\n    })\n  }\n}\n\n// Export singleton instance\nexport const api = new ApiClient(API_BASE_URL)\n\n// Utility functions\nexport const formatSalary = (min?: number, max?: number, negotiable?: boolean): string => {\n  if (negotiable) return 'Negotiable'\n  if (!min && !max) return 'Not specified'\n  if (min && max) return `$${min.toLocaleString()} - $${max.toLocaleString()}`\n  if (min) return `$${min.toLocaleString()}+`\n  if (max) return `Up to $${max.toLocaleString()}`\n  return 'Not specified'\n}\n\nexport const formatDate = (dateString: string): string => {\n  return new Date(dateString).toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  })\n}\n\nexport const getJobTypeLabel = (type: string): string => {\n  const labels: Record<string, string> = {\n    'FULL_TIME': 'Full Time',\n    'PART_TIME': 'Part Time',\n    'CONTRACT': 'Contract',\n    'INTERNSHIP': 'Internship',\n    // Legacy support\n    'full-time': 'Full Time',\n    'part-time': 'Part Time',\n    'contract': 'Contract',\n    'freelance': 'Freelance',\n    'internship': 'Internship',\n  }\n  return labels[type] || type\n}\n\nexport const getExperienceLabel = (level: string): string => {\n  const labels: Record<string, string> = {\n    'STUDENT': 'Student / Currently Studying',\n    'FRESHER': 'Fresher',\n    'INTERNSHIP_ONLY': 'Internship Experience Only',\n    'ZERO_TO_ONE_YEAR': '0–1 Year',\n    'ONE_TO_THREE_YEARS': '1–3 Years',\n    'THREE_TO_FIVE_YEARS': '3–5 Years',\n    'FIVE_PLUS_YEARS': '5+ Years',\n    // Legacy support\n    'THREE_PLUS_YEARS': '3+ Years',\n    'entry': 'Entry Level',\n    'mid': 'Mid Level',\n    'senior': 'Senior Level',\n    'lead': 'Lead',\n    'executive': 'Executive',\n  }\n  return labels[level] || level\n}\n\nexport const getWorkTypeLabel = (type: string): string => {\n  const labels: Record<string, string> = {\n    'ONSITE': 'On-site',\n    'REMOTE': 'Remote',\n    'HYBRID': 'Hybrid',\n    // Legacy support\n    'onsite': 'On-site',\n    'remote': 'Remote',\n    'hybrid': 'Hybrid',\n  }\n  return labels[type] || type\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA,MAAM,eAAe,iEAAmC;AAuIxD,mBAAmB;AACnB,MAAM;IACI,QAAe;IACf,QAAuB,KAAI;IAEnC,YAAY,OAAe,CAAE;QAC3B,IAAI,CAAC,OAAO,GAAG;QACf,kDAAkD;QAClD,uCAAmC;;QAEnC;IACF;IAEA,SAAS,KAAa,EAAE;QACtB,IAAI,CAAC,KAAK,GAAG;QACb,uCAAmC;;QAEnC;IACF;IAEA,aAAa;QACX,IAAI,CAAC,KAAK,GAAG;QACb,uCAAmC;;QAEnC;IACF;IAEA,MAAc,QACZ,QAAgB,EAChB,UAAuB,CAAC,CAAC,EACb;QACZ,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU;QACxC,MAAM,UAAkC;YACtC,gBAAgB;YAChB,GAAI,QAAQ,OAAO;QACrB;QAEA,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,QAAQ,aAAa,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,EAAE;QAChD;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC,GAAG,OAAO;gBACV;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC;wBACnD,SAAS,SAAS,MAAM,KAAK,MAAM,uBAAuB;wBAC1D,QAAQ,SAAS,MAAM;oBACzB,CAAC;gBAED,MAAM,QAAQ,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,EAAE;gBACpE,MAAc,MAAM,GAAG,SAAS,MAAM;gBACtC,MAAc,IAAI,GAAG,UAAU,IAAI;gBACrC,MAAM;YACR;YAEA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,wBAAwB;YACxB,IAAI,iBAAiB,aAAa,CAAC,UAAU,MAAM,EAAE;gBACnD,MAAM,eAAe,IAAI,MAAM;gBAC7B,aAAqB,MAAM,GAAG;gBAChC,MAAM;YACR;YACA,MAAM;QACR;IACF;IAEA,iBAAiB;IACjB,MAAM,MAAM,KAAa,EAAE,QAAgB,EAAyB;QAClE,OAAO,IAAI,CAAC,OAAO,CAAe,eAAe;YAC/C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAO;YAAS;QACzC;IACF;IAEA,MAAM,SAAS,IAMd,EAAyB;QACxB,OAAO,IAAI,CAAC,OAAO,CAAe,kBAAkB;YAClD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,aAA4B;QAChC,MAAM,WAAW,MAAM,IAAI,CAAC,OAAO,CAAiB;QACpD,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,cAAc,IAA0B,EAA4C;QACxF,OAAO,IAAI,CAAC,OAAO,CAAkC,iBAAiB;YACpE,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,iBAAiB;IACjB,MAAM,QACJ,UAAsB,CAAC,CAAC,EACxB,OAAO,CAAC,EACR,QAAQ,EAAE,EACa;QACvB,MAAM,SAAS,IAAI,gBAAgB;YACjC,MAAM,KAAK,QAAQ;YACnB,OAAO,MAAM,QAAQ;YACrB,GAAG,OAAO,WAAW,CACnB,OAAO,OAAO,CAAC,SAAS,MAAM,CAAC,CAAC,GAAG,MAAM,GAAK,UAAU,aAAa,UAAU,IAChF;QACH;QAEA,OAAO,IAAI,CAAC,OAAO,CAAe,CAAC,MAAM,EAAE,QAAQ;IACrD;IAEA,MAAM,OAAO,IAAY,EAA8C;QACrE,OAAO,IAAI,CAAC,OAAO,CAAoC,CAAC,MAAM,EAAE,MAAM;IACxE;IAEA,MAAM,kBAAkC;QACtC,IAAI;YACF,MAAM,SAAS,MAAM,IAAI,CAAC,OAAO,CAAkB;YACnD,OAAO,MAAM,OAAO,CAAC,OAAO,IAAI,IAAI,OAAO,IAAI,GAAG,EAAE;QACtD,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,kCAAkC;YAC/C,OAAO,EAAE;QACX;IACF;IAEA,MAAM,WAAW,KAAa,EAAE,WAAoB,EAA6D;QAC/G,OAAO,IAAI,CAAC,OAAO,CAAmD,CAAC,MAAM,EAAE,MAAM,MAAM,CAAC,EAAE;YAC5F,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE,SAAS;YAAY;QAC9C;IACF;IAEA,MAAM,oBAA6F;QACjG,OAAO,IAAI,CAAC,OAAO,CAAiE;IACtF;IAEA,MAAM,mBAAqE;QACzE,OAAO,IAAI,CAAC,OAAO,CAA0C;IAC/D;IAEA,iBAAiB;IACjB,MAAM,SAAS,OAAO,CAAC,EAAE,QAAQ,EAAE,EAA0B;QAC3D,MAAM,SAAS,IAAI,gBAAgB;YACjC,MAAM,KAAK,QAAQ;YACnB,OAAO,MAAM,QAAQ;QACvB;QAEA,OAAO,IAAI,CAAC,OAAO,CAAgB,CAAC,OAAO,EAAE,QAAQ;IACvD;IAEA,MAAM,QAAQ,IAAY,EAAqB;QAC7C,OAAO,IAAI,CAAC,OAAO,CAAW,CAAC,OAAO,EAAE,MAAM;IAChD;IAEA,cAAc;IACd,MAAM,WAAW,IAAU,EAAE,IAAkC,EAA4B;QACzF,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QAExB,MAAM,UAAkC,CAAC;QACzC,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,QAAQ,aAAa,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,EAAE;QAChD;QAEA,uCAAuC;QACvC,MAAM,WAAW,SAAS,WAAW,mBAAmB;QAExD,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU,EAAE;YACzD,QAAQ;YACR;YACA,MAAM;QACR;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,QAAQ,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC;oBAAE,SAAS;gBAAgB,CAAC;YAC7E,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,EAAE;QAC5D;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAElC,oEAAoE;QACpE,IAAI,SAAS,UAAU;YACrB,OAAO;gBAAE,KAAK,OAAO,MAAM;YAAC;QAC9B,OAAO;YACL,OAAO;gBAAE,KAAK,OAAO,cAAc;YAAC;QACtC;IACF;IAEA,eAAe;IACf,MAAM,kBAAkB,IAKvB,EAAkD;QACjD,OAAO,IAAI,CAAC,OAAO,CAAwC,YAAY;YACrE,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,qBAAqB;IACrB,MAAM,sBAAsB,KAAa,EAAoD;QAC3F,OAAO,IAAI,CAAC,OAAO,CAA0C,2BAA2B;YACtF,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAM;QAC/B;IACF;IAEA,MAAM,wBAAwB,KAAa,EAAoD;QAC7F,OAAO,IAAI,CAAC,qBAAqB,CAAC;IACpC;IAEA,MAAM,YAAY,KAAa,EAA0D;QACvF,OAAO,IAAI,CAAC,OAAO,CAAgD,sBAAsB;YACvF,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAM;QAC/B;IACF;IAEA,iBAAiB;IACjB,MAAM,eAAe,KAAa,EAAqD;QACrF,OAAO,IAAI,CAAC,OAAO,CAA2C,yBAAyB;YACrF,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAM;QAC/B;IACF;IAEA,MAAM,cAAc,KAAa,EAAE,QAAgB,EAA0D;QAC3G,OAAO,IAAI,CAAC,OAAO,CAAgD,wBAAwB;YACzF,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAO;YAAS;QACzC;IACF;AACF;AAGO,MAAM,MAAM,IAAI,UAAU;AAG1B,MAAM,eAAe,CAAC,KAAc,KAAc;IACvD,IAAI,YAAY,OAAO;IACvB,IAAI,CAAC,OAAO,CAAC,KAAK,OAAO;IACzB,IAAI,OAAO,KAAK,OAAO,CAAC,CAAC,EAAE,IAAI,cAAc,GAAG,IAAI,EAAE,IAAI,cAAc,IAAI;IAC5E,IAAI,KAAK,OAAO,CAAC,CAAC,EAAE,IAAI,cAAc,GAAG,CAAC,CAAC;IAC3C,IAAI,KAAK,OAAO,CAAC,OAAO,EAAE,IAAI,cAAc,IAAI;IAChD,OAAO;AACT;AAEO,MAAM,aAAa,CAAC;IACzB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;QACtD,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,MAAM,kBAAkB,CAAC;IAC9B,MAAM,SAAiC;QACrC,aAAa;QACb,aAAa;QACb,YAAY;QACZ,cAAc;QACd,iBAAiB;QACjB,aAAa;QACb,aAAa;QACb,YAAY;QACZ,aAAa;QACb,cAAc;IAChB;IACA,OAAO,MAAM,CAAC,KAAK,IAAI;AACzB;AAEO,MAAM,qBAAqB,CAAC;IACjC,MAAM,SAAiC;QACrC,WAAW;QACX,WAAW;QACX,mBAAmB;QACnB,oBAAoB;QACpB,sBAAsB;QACtB,uBAAuB;QACvB,mBAAmB;QACnB,iBAAiB;QACjB,oBAAoB;QACpB,SAAS;QACT,OAAO;QACP,UAAU;QACV,QAAQ;QACR,aAAa;IACf;IACA,OAAO,MAAM,CAAC,MAAM,IAAI;AAC1B;AAEO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,SAAiC;QACrC,UAAU;QACV,UAAU;QACV,UAAU;QACV,iBAAiB;QACjB,UAAU;QACV,UAAU;QACV,UAAU;IACZ;IACA,OAAO,MAAM,CAAC,KAAK,IAAI;AACzB", "debugId": null}}, {"offset": {"line": 325, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/job-portal/client/src/app/jobs/%5Bslug%5D/page.tsx"], "sourcesContent": ["import { Metadata } from 'next'\nimport { notFound } from 'next/navigation'\nimport { JobDetailsClient } from '@/components/jobs/job-details-client'\nimport { api } from '@/lib/api'\n\ninterface JobPageProps {\n  params: Promise<{ slug: string }>\n}\n\nexport async function generateMetadata({ params }: JobPageProps): Promise<Metadata> {\n  const resolvedParams = await params\n  console.log(resolvedParams.slug)\n  try {\n    const jobData = await api.getJob(resolvedParams.slug)\n    const job = jobData?.job\n    console.log(job)\n\n    return {\n      title: `${job.title} at ${job.companyName} | JobPortal`,\n      description: job.description.replace(/<[^>]*>/g, '').substring(0, 160),\n      keywords: [job.title, job.companyName, job.category, job.location, 'job', 'career'],\n      openGraph: {\n        title: `${job.title} at ${job.companyName}`,\n        description: job.description.replace(/<[^>]*>/g, '').substring(0, 160),\n        type: 'article',\n        locale: 'en_US',\n        images: job.companyLogo ? [{ url: job.companyLogo }] : [],\n      },\n      twitter: {\n        card: 'summary_large_image',\n        title: `${job.title} at ${job.companyName}`,\n        description: job.description.replace(/<[^>]*>/g, '').substring(0, 160),\n        images: job.companyLogo ? [job.companyLogo] : [],\n      },\n      alternates: {\n        canonical: `/jobs/${job.slug}`,\n      },\n    }\n  } catch {\n    return {\n      title: 'Job Not Found | JobPortal',\n      description: 'The job you are looking for could not be found.',\n    }\n  }\n}\n\nexport default async function JobPage({ params }: JobPageProps) {\n  try {\n    const resolvedParams = await params\n    const jobData = await api.getJob(resolvedParams.slug)\n    console.log(jobData?.hasApplied)\n    const job = jobData?.job\n\n    const structuredData = {\n      '@context': 'https://schema.org',\n      '@type': 'JobPosting',\n      title: job.title,\n      description: job.description.replace(/<[^>]*>/g, ''),\n      identifier: {\n        '@type': 'PropertyValue',\n        name: job.companyName,\n        value: job.id,\n      },\n      datePosted: job.createdAt,\n      validThrough: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),\n      employmentType: job.jobType.toUpperCase().replace('-', '_'),\n      hiringOrganization: {\n        '@type': 'Organization',\n        name: job.companyName,\n        logo: job.companyLogo,\n      },\n      jobLocation: {\n        '@type': 'Place',\n        address: {\n          '@type': 'PostalAddress',\n          addressLocality: job.location,\n        },\n      },\n      baseSalary: job.salaryMin && job.salaryMax ? {\n        '@type': 'MonetaryAmount',\n        currency: 'USD',\n        value: {\n          '@type': 'QuantitativeValue',\n          minValue: job.salaryMin,\n          maxValue: job.salaryMax,\n          unitText: 'YEAR',\n        },\n      } : undefined,\n      workHours: job.jobType === 'FULL_TIME' ? '40 hours per week' : undefined,\n    }\n\n    return (\n      <>\n        <script\n          type=\"application/ld+json\"\n          dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}\n        />\n        <JobDetailsClient job={job} hasApplied={jobData?.hasApplied} />\n      </>\n    )\n  } catch {\n    notFound()\n  }\n}\n"], "names": [], "mappings": ";;;;;AACA;AAAA;AACA;AACA;;;;;AAMO,eAAe,iBAAiB,EAAE,MAAM,EAAgB;IAC7D,MAAM,iBAAiB,MAAM;IAC7B,QAAQ,GAAG,CAAC,eAAe,IAAI;IAC/B,IAAI;QACF,MAAM,UAAU,MAAM,iHAAA,CAAA,MAAG,CAAC,MAAM,CAAC,eAAe,IAAI;QACpD,MAAM,MAAM,SAAS;QACrB,QAAQ,GAAG,CAAC;QAEZ,OAAO;YACL,OAAO,GAAG,IAAI,KAAK,CAAC,IAAI,EAAE,IAAI,WAAW,CAAC,YAAY,CAAC;YACvD,aAAa,IAAI,WAAW,CAAC,OAAO,CAAC,YAAY,IAAI,SAAS,CAAC,GAAG;YAClE,UAAU;gBAAC,IAAI,KAAK;gBAAE,IAAI,WAAW;gBAAE,IAAI,QAAQ;gBAAE,IAAI,QAAQ;gBAAE;gBAAO;aAAS;YACnF,WAAW;gBACT,OAAO,GAAG,IAAI,KAAK,CAAC,IAAI,EAAE,IAAI,WAAW,EAAE;gBAC3C,aAAa,IAAI,WAAW,CAAC,OAAO,CAAC,YAAY,IAAI,SAAS,CAAC,GAAG;gBAClE,MAAM;gBACN,QAAQ;gBACR,QAAQ,IAAI,WAAW,GAAG;oBAAC;wBAAE,KAAK,IAAI,WAAW;oBAAC;iBAAE,GAAG,EAAE;YAC3D;YACA,SAAS;gBACP,MAAM;gBACN,OAAO,GAAG,IAAI,KAAK,CAAC,IAAI,EAAE,IAAI,WAAW,EAAE;gBAC3C,aAAa,IAAI,WAAW,CAAC,OAAO,CAAC,YAAY,IAAI,SAAS,CAAC,GAAG;gBAClE,QAAQ,IAAI,WAAW,GAAG;oBAAC,IAAI,WAAW;iBAAC,GAAG,EAAE;YAClD;YACA,YAAY;gBACV,WAAW,CAAC,MAAM,EAAE,IAAI,IAAI,EAAE;YAChC;QACF;IACF,EAAE,OAAM;QACN,OAAO;YACL,OAAO;YACP,aAAa;QACf;IACF;AACF;AAEe,eAAe,QAAQ,EAAE,MAAM,EAAgB;IAC5D,IAAI;QACF,MAAM,iBAAiB,MAAM;QAC7B,MAAM,UAAU,MAAM,iHAAA,CAAA,MAAG,CAAC,MAAM,CAAC,eAAe,IAAI;QACpD,QAAQ,GAAG,CAAC,SAAS;QACrB,MAAM,MAAM,SAAS;QAErB,MAAM,iBAAiB;YACrB,YAAY;YACZ,SAAS;YACT,OAAO,IAAI,KAAK;YAChB,aAAa,IAAI,WAAW,CAAC,OAAO,CAAC,YAAY;YACjD,YAAY;gBACV,SAAS;gBACT,MAAM,IAAI,WAAW;gBACrB,OAAO,IAAI,EAAE;YACf;YACA,YAAY,IAAI,SAAS;YACzB,cAAc,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,WAAW;YACzE,gBAAgB,IAAI,OAAO,CAAC,WAAW,GAAG,OAAO,CAAC,KAAK;YACvD,oBAAoB;gBAClB,SAAS;gBACT,MAAM,IAAI,WAAW;gBACrB,MAAM,IAAI,WAAW;YACvB;YACA,aAAa;gBACX,SAAS;gBACT,SAAS;oBACP,SAAS;oBACT,iBAAiB,IAAI,QAAQ;gBAC/B;YACF;YACA,YAAY,IAAI,SAAS,IAAI,IAAI,SAAS,GAAG;gBAC3C,SAAS;gBACT,UAAU;gBACV,OAAO;oBACL,SAAS;oBACT,UAAU,IAAI,SAAS;oBACvB,UAAU,IAAI,SAAS;oBACvB,UAAU;gBACZ;YACF,IAAI;YACJ,WAAW,IAAI,OAAO,KAAK,cAAc,sBAAsB;QACjE;QAEA,qBACE;;8BACE,8OAAC;oBACC,MAAK;oBACL,yBAAyB;wBAAE,QAAQ,KAAK,SAAS,CAAC;oBAAgB;;;;;;8BAEpE,8OAAC,sJAAA,CAAA,mBAAgB;oBAAC,KAAK;oBAAK,YAAY,SAAS;;;;;;;;IAGvD,EAAE,OAAM;QACN,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;AACF", "debugId": null}}]}