{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/job-portal/client/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport const formatDate = (dateString: string): string => {\n  const date = new Date(dateString)\n  return date.toLocaleDateString(\"en-US\", {\n    year: \"numeric\",\n    month: \"short\",\n    day: \"numeric\"\n  })\n}\n\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,MAAM,aAAa,CAAC;IACzB,MAAM,OAAO,IAAI,KAAK;IACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/job-portal/client/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/job-portal/client/src/app/not-found.tsx"], "sourcesContent": ["import Link from 'next/link'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Home, Search, ArrowLeft } from 'lucide-react'\n\nexport default function NotFound() {\n  return (\n    <div className=\"min-h-screen bg-background flex items-center justify-center\">\n      <div className=\"text-center px-4\">\n        <div className=\"mb-8\">\n          <h1 className=\"text-9xl font-bold text-primary/20 mb-4\">404</h1>\n          <h2 className=\"text-3xl md:text-4xl font-bold text-foreground mb-4\">\n            Page Not Found\n          </h2>\n          <p className=\"text-xl text-muted-foreground mb-8 max-w-md mx-auto\">\n            Sorry, we couldn&apos;t find the page you&apos;re looking for. \n            It might have been moved, deleted, or you entered the wrong URL.\n          </p>\n        </div>\n        \n        <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n          <Button asChild size=\"lg\">\n            <Link href=\"/\">\n              <Home className=\"mr-2 h-5 w-5\" />\n              Go Home\n            </Link>\n          </Button>\n          <Button asChild variant=\"outline\" size=\"lg\">\n            <Link href=\"/jobs\">\n              <Search className=\"mr-2 h-5 w-5\" />\n              Browse Jobs\n            </Link>\n          </Button>\n        </div>\n        \n        <div className=\"mt-8\">\n          <Button variant=\"ghost\" asChild>\n            <Link href=\"javascript:history.back()\">\n              <ArrowLeft className=\"mr-2 h-4 w-4\" />\n              Go Back\n            </Link>\n          </Button>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AAAA;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA0C;;;;;;sCACxD,8OAAC;4BAAG,WAAU;sCAAsD;;;;;;sCAGpE,8OAAC;4BAAE,WAAU;sCAAsD;;;;;;;;;;;;8BAMrE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kIAAA,CAAA,SAAM;4BAAC,OAAO;4BAAC,MAAK;sCACnB,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;;kDACT,8OAAC,mMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;sCAIrC,8OAAC,kIAAA,CAAA,SAAM;4BAAC,OAAO;4BAAC,SAAQ;4BAAU,MAAK;sCACrC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;;kDACT,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;8BAMzC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAQ,OAAO;kCAC7B,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;;8CACT,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpD", "debugId": null}}]}