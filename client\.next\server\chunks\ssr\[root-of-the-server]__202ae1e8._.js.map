{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/job-portal/client/src/lib/query-client.tsx"], "sourcesContent": ["'use client'\n\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query'\nimport { ReactQueryDevtools } from '@tanstack/react-query-devtools'\nimport { useState, ReactNode } from 'react'\n\nexport function QueryProvider({ children }: { children: ReactNode }) {\n  const [queryClient] = useState(\n    () =>\n      new QueryClient({\n        defaultOptions: {\n          queries: {\n            staleTime: 5 * 60 * 1000, // 5 minutes\n            gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)\n            retry: (failureCount, error) => {\n              // Don't retry on 401/403 errors\n              if (error instanceof Error && error.message.includes('401')) {\n                return false\n              }\n              if (error instanceof Error && error.message.includes('403')) {\n                return false\n              }\n              return failureCount < 3\n            },\n          },\n          mutations: {\n            retry: false,\n          },\n        },\n      })\n  )\n\n  return (\n    <QueryClientProvider client={queryClient}>\n      {children}\n      <ReactQueryDevtools initialIsOpen={false} />\n    </QueryClientProvider>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AAJA;;;;;AAMO,SAAS,cAAc,EAAE,QAAQ,EAA2B;IACjE,MAAM,CAAC,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAC3B,IACE,IAAI,6KAAA,CAAA,cAAW,CAAC;YACd,gBAAgB;gBACd,SAAS;oBACP,WAAW,IAAI,KAAK;oBACpB,QAAQ,KAAK,KAAK;oBAClB,OAAO,CAAC,cAAc;wBACpB,gCAAgC;wBAChC,IAAI,iBAAiB,SAAS,MAAM,OAAO,CAAC,QAAQ,CAAC,QAAQ;4BAC3D,OAAO;wBACT;wBACA,IAAI,iBAAiB,SAAS,MAAM,OAAO,CAAC,QAAQ,CAAC,QAAQ;4BAC3D,OAAO;wBACT;wBACA,OAAO,eAAe;oBACxB;gBACF;gBACA,WAAW;oBACT,OAAO;gBACT;YACF;QACF;IAGJ,qBACE,8OAAC,sLAAA,CAAA,sBAAmB;QAAC,QAAQ;;YAC1B;0BACD,8OAAC,oLAAA,CAAA,qBAAkB;gBAAC,eAAe;;;;;;;;;;;;AAGzC", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/job-portal/client/src/components/ui/sonner.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useTheme } from \"next-themes\"\nimport { Toaster as Son<PERSON>, ToasterP<PERSON> } from \"sonner\"\n\nconst Toaster = ({ ...props }: ToasterProps) => {\n  const { theme = \"system\" } = useTheme()\n\n  return (\n    <Sonner\n      theme={theme as ToasterProps[\"theme\"]}\n      className=\"toaster group\"\n      style={\n        {\n          \"--normal-bg\": \"var(--popover)\",\n          \"--normal-text\": \"var(--popover-foreground)\",\n          \"--normal-border\": \"var(--border)\",\n        } as React.CSSProperties\n      }\n      {...props}\n    />\n  )\n}\n\nexport { Toaster }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,UAAU,CAAC,EAAE,GAAG,OAAqB;IACzC,MAAM,EAAE,QAAQ,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IAEpC,qBACE,8OAAC,wIAAA,CAAA,UAAM;QACL,OAAO;QACP,WAAU;QACV,OACE;YACE,eAAe;YACf,iBAAiB;YACjB,mBAAmB;QACrB;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 132, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/job-portal/client/src/lib/api.ts"], "sourcesContent": ["const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api'\n\n// Types\nexport interface User {\n  id: string\n  email: string\n  phone?: string\n  experienceLevel?: 'STUDENT' | 'FRESHER' | 'INTERNSHIP_ONLY' | 'ZERO_TO_ONE_YEAR' | 'ONE_TO_THREE_YEARS' | 'THREE_TO_FIVE_YEARS' | 'FIVE_PLUS_YEARS' | 'THREE_PLUS_YEARS'\n  profileCompleted: boolean\n  // Flat structure from API\n  firstName?: string\n  lastName?: string\n  bio?: string\n  skills?: string[]\n  experience?: string\n  education?: string\n  location?: string\n  website?: string\n  linkedin?: string\n  github?: string\n  profilePicture?: string\n  resume?: string\n  // Legacy nested structure for backward compatibility\n  profile?: UserProfile\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface UserProfile {\n  id?: string\n  userId?: string\n  firstName?: string\n  lastName?: string\n  phone?: string\n  bio?: string\n  skills?: string[]\n  experience?: string\n  education?: string\n  location?: string\n  website?: string\n  linkedin?: string\n  github?: string\n  resume?: string\n  profilePicture?: string\n  resumeUrl?: string\n  profilePictureUrl?: string\n  createdAt?: string\n  updatedAt?: string\n}\n\nexport interface Job {\n  id: string\n  title: string\n  slug: string\n  description: string\n  requirements?: string[]\n  responsibilities?: string[]\n  category: string\n  location: string\n  workLocationType?: 'ONSITE' | 'REMOTE' | 'HYBRID'\n  jobType: 'FULL_TIME' | 'PART_TIME' | 'CONTRACT' | 'INTERNSHIP'\n  experienceLevel: 'STUDENT' | 'FRESHER' | 'INTERNSHIP_ONLY' | 'ZERO_TO_ONE_YEAR' | 'ONE_TO_THREE_YEARS' | 'THREE_TO_FIVE_YEARS' | 'FIVE_PLUS_YEARS' | 'THREE_PLUS_YEARS'\n  salaryMin?: number\n  salaryMax?: number\n  salaryNegotiable?: boolean\n  currency?: string\n  companyName: string\n  companyLogo?: string\n  isFeatured?: boolean\n  isActive?: boolean\n  createdAt: string\n  updatedAt?: string\n}\n\nexport interface BlogPost {\n  id: string\n  title: string\n  slug: string\n  content?: string\n  excerpt?: string\n  featuredImage?: string\n  author?: string\n  published?: boolean\n  publishedAt?: string\n  createdAt: string\n  updatedAt?: string\n}\n\nexport interface JobApplication {\n  id: string\n  jobId: string\n  userId: string\n  status: 'PENDING' | 'REVIEWED' | 'SHORTLISTED' | 'ACCEPTED' | 'REJECTED' | 'HIRED'\n  message?: string\n  coverLetter?: string // For backward compatibility\n  job: Job\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface AuthResponse {\n  user: User\n  token: string\n}\n\nexport interface PaginationInfo {\n  page: number\n  limit: number\n  total: number\n  totalPages: number\n  hasNext: boolean\n  hasPrev: boolean\n}\n\nexport interface JobsResponse {\n  jobs: Job[]\n  pagination: PaginationInfo\n}\n\nexport interface BlogsResponse {\n  blogs: BlogPost[]\n  pagination: PaginationInfo\n}\n\nexport interface JobFilters {\n  search?: string\n  category?: string\n  location?: string\n  workLocationType?: string\n  jobType?: string\n  experienceLevel?: string\n  salaryMin?: number\n  salaryMax?: number\n}\n\n// API Client Class\nclass ApiClient {\n  private baseURL: string\n  private token: string | null = null\n\n  constructor(baseURL: string) {\n    this.baseURL = baseURL\n    // Initialize token from localStorage if available\n    if (typeof window !== 'undefined') {\n      this.token = localStorage.getItem('auth_token')\n    }\n  }\n\n  setToken(token: string) {\n    this.token = token\n    if (typeof window !== 'undefined') {\n      localStorage.setItem('auth_token', token)\n    }\n  }\n\n  clearToken() {\n    this.token = null\n    if (typeof window !== 'undefined') {\n      localStorage.removeItem('auth_token')\n    }\n  }\n\n  private async request<T>(\n    endpoint: string,\n    options: RequestInit = {}\n  ): Promise<T> {\n    const url = `${this.baseURL}${endpoint}`\n    const headers: Record<string, string> = {\n      'Content-Type': 'application/json',\n      ...(options.headers as Record<string, string>),\n    }\n\n    if (this.token) {\n      headers.Authorization = `Bearer ${this.token}`\n    }\n\n    try {\n      const response = await fetch(url, {\n        ...options,\n        headers,\n      })\n\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({\n          message: response.status === 404 ? 'Resource not found' : 'Network error',\n          status: response.status\n        }))\n\n        const error = new Error(errorData.message || `HTTP ${response.status}`)\n        ;(error as any).status = response.status\n        ;(error as any).code = errorData.code\n        throw error\n      }\n\n      return response.json()\n    } catch (error) {\n      // Handle network errors\n      if (error instanceof TypeError || !navigator.onLine) {\n        const networkError = new Error('Network error. Please check your internet connection.')\n        ;(networkError as any).status = 0\n        throw networkError\n      }\n      throw error\n    }\n  }\n\n  // Auth endpoints\n  async login(email: string, password: string): Promise<AuthResponse> {\n    return this.request<AuthResponse>('/auth/login', {\n      method: 'POST',\n      body: JSON.stringify({ email, password }),\n    })\n  }\n\n  async register(data: {\n    email: string\n    password: string\n    phone?: string\n    experienceLevel?: string\n  }): Promise<AuthResponse> {\n    return this.request<AuthResponse>('/auth/register', {\n      method: 'POST',\n      body: JSON.stringify(data),\n    })\n  }\n\n  async getProfile(): Promise<User> {\n    const response = await this.request<{ user: User }>('/auth/me')\n    return response.user\n  }\n\n  async updateProfile(data: Partial<UserProfile>): Promise<{ user: User; message: string }> {\n    return this.request<{ user: User; message: string }>('/user/profile', {\n      method: 'PUT',\n      body: JSON.stringify(data),\n    })\n  }\n\n  // Jobs endpoints\n  async getJobs(\n    filters: JobFilters = {},\n    page = 1,\n    limit = 12\n  ): Promise<JobsResponse> {\n    const params = new URLSearchParams({\n      page: page.toString(),\n      limit: limit.toString(),\n      ...Object.fromEntries(\n        Object.entries(filters).filter(([, value]) => value !== undefined && value !== '')\n      ),\n    })\n\n    return this.request<JobsResponse>(`/jobs?${params}`)\n  }\n\n  async getJob(slug: string): Promise<{ job: Job; hasApplied: boolean }> {\n    return this.request<{ job: Job; hasApplied: boolean }>(`/jobs/${slug}`)\n  }\n\n  async getFeaturedJobs(): Promise<Job[]> {\n    try {\n      const result = await this.request<{ jobs: Job[] }>('/jobs/featured')\n      return Array.isArray(result.jobs) ? result.jobs : []\n    } catch (error) {\n      console.warn('Failed to fetch featured jobs:', error)\n      return []\n    }\n  }\n\n  async applyToJob(jobId: string, coverLetter?: string): Promise<{ application: JobApplication; message: string }> {\n    return this.request<{ application: JobApplication; message: string }>(`/jobs/${jobId}/apply`, {\n      method: 'POST',\n      body: JSON.stringify({ message: coverLetter }),\n    })\n  }\n\n  async getMyApplications(): Promise<{ applications: JobApplication[]; pagination: PaginationInfo }> {\n    return this.request<{ applications: JobApplication[]; pagination: PaginationInfo }>('/user/applications')\n  }\n\n  async getJobCategories(): Promise<{ categories: string[]; total: number }> {\n    return this.request<{ categories: string[]; total: number }>('/jobs/categories')\n  }\n\n  // Blog endpoints\n  async getBlogs(page = 1, limit = 10): Promise<BlogsResponse> {\n    const params = new URLSearchParams({\n      page: page.toString(),\n      limit: limit.toString(),\n    })\n\n    return this.request<BlogsResponse>(`/blogs?${params}`)\n  }\n\n  async getBlog(slug: string): Promise<BlogPost> {\n    return this.request<BlogPost>(`/blogs/${slug}`)\n  }\n\n  // File upload\n  async uploadFile(file: File, type: 'resume' | 'profile-picture'): Promise<{ url: string }> {\n    const formData = new FormData()\n    formData.append('file', file)\n\n    const headers: Record<string, string> = {}\n    if (this.token) {\n      headers.Authorization = `Bearer ${this.token}`\n    }\n\n    // Use specific endpoints based on type\n    const endpoint = type === 'resume' ? '/upload/resume' : '/upload/profile-picture'\n\n    const response = await fetch(`${this.baseURL}${endpoint}`, {\n      method: 'POST',\n      headers,\n      body: formData,\n    })\n\n    if (!response.ok) {\n      const error = await response.json().catch(() => ({ message: 'Upload failed' }))\n      throw new Error(error.message || `HTTP ${response.status}`)\n    }\n\n    const result = await response.json()\n\n    // Normalize response format - backend returns different field names\n    if (type === 'resume') {\n      return { url: result.resume }\n    } else {\n      return { url: result.profilePicture }\n    }\n  }\n\n  // Contact form\n  async submitContactForm(data: {\n    name: string\n    email: string\n    subject: string\n    message: string\n  }): Promise<{ success: boolean; message: string }> {\n    return this.request<{ success: boolean; message: string }>('/contact', {\n      method: 'POST',\n      body: JSON.stringify(data),\n    })\n  }\n\n  // Email verification\n  async sendVerificationEmail(email: string): Promise<{ message: string; emailSent: boolean }> {\n    return this.request<{ message: string; emailSent: boolean }>('/auth/send-verification', {\n      method: 'POST',\n      body: JSON.stringify({ email }),\n    })\n  }\n\n  async resendVerificationEmail(email: string): Promise<{ message: string; emailSent: boolean }> {\n    return this.sendVerificationEmail(email)\n  }\n\n  async verifyEmail(token: string): Promise<{ message: string; user: any; token: string }> {\n    return this.request<{ message: string; user: any; token: string }>('/auth/verify-email', {\n      method: 'POST',\n      body: JSON.stringify({ token }),\n    })\n  }\n\n  // Password reset\n  async forgotPassword(email: string): Promise<{ message: string; emailSent?: boolean }> {\n    return this.request<{ message: string; emailSent?: boolean }>('/auth/forgot-password', {\n      method: 'POST',\n      body: JSON.stringify({ email }),\n    })\n  }\n\n  async resetPassword(token: string, password: string): Promise<{ message: string; user: any; token: string }> {\n    return this.request<{ message: string; user: any; token: string }>('/auth/reset-password', {\n      method: 'POST',\n      body: JSON.stringify({ token, password }),\n    })\n  }\n}\n\n// Export singleton instance\nexport const api = new ApiClient(API_BASE_URL)\n\n// Utility functions\nexport const formatSalary = (min?: number, max?: number, negotiable?: boolean): string => {\n  if (negotiable) return 'Negotiable'\n  if (!min && !max) return 'Not specified'\n  if (min && max) return `$${min.toLocaleString()} - $${max.toLocaleString()}`\n  if (min) return `$${min.toLocaleString()}+`\n  if (max) return `Up to $${max.toLocaleString()}`\n  return 'Not specified'\n}\n\nexport const formatDate = (dateString: string): string => {\n  return new Date(dateString).toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  })\n}\n\nexport const getJobTypeLabel = (type: string): string => {\n  const labels: Record<string, string> = {\n    'FULL_TIME': 'Full Time',\n    'PART_TIME': 'Part Time',\n    'CONTRACT': 'Contract',\n    'INTERNSHIP': 'Internship',\n    // Legacy support\n    'full-time': 'Full Time',\n    'part-time': 'Part Time',\n    'contract': 'Contract',\n    'freelance': 'Freelance',\n    'internship': 'Internship',\n  }\n  return labels[type] || type\n}\n\nexport const getExperienceLabel = (level: string): string => {\n  const labels: Record<string, string> = {\n    'STUDENT': 'Student / Currently Studying',\n    'FRESHER': 'Fresher',\n    'INTERNSHIP_ONLY': 'Internship Experience Only',\n    'ZERO_TO_ONE_YEAR': '0–1 Year',\n    'ONE_TO_THREE_YEARS': '1–3 Years',\n    'THREE_TO_FIVE_YEARS': '3–5 Years',\n    'FIVE_PLUS_YEARS': '5+ Years',\n    // Legacy support\n    'THREE_PLUS_YEARS': '3+ Years',\n    'entry': 'Entry Level',\n    'mid': 'Mid Level',\n    'senior': 'Senior Level',\n    'lead': 'Lead',\n    'executive': 'Executive',\n  }\n  return labels[level] || level\n}\n\nexport const getWorkTypeLabel = (type: string): string => {\n  const labels: Record<string, string> = {\n    'ONSITE': 'On-site',\n    'REMOTE': 'Remote',\n    'HYBRID': 'Hybrid',\n    // Legacy support\n    'onsite': 'On-site',\n    'remote': 'Remote',\n    'hybrid': 'Hybrid',\n  }\n  return labels[type] || type\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA,MAAM,eAAe,iEAAmC;AAuIxD,mBAAmB;AACnB,MAAM;IACI,QAAe;IACf,QAAuB,KAAI;IAEnC,YAAY,OAAe,CAAE;QAC3B,IAAI,CAAC,OAAO,GAAG;QACf,kDAAkD;QAClD,uCAAmC;;QAEnC;IACF;IAEA,SAAS,KAAa,EAAE;QACtB,IAAI,CAAC,KAAK,GAAG;QACb,uCAAmC;;QAEnC;IACF;IAEA,aAAa;QACX,IAAI,CAAC,KAAK,GAAG;QACb,uCAAmC;;QAEnC;IACF;IAEA,MAAc,QACZ,QAAgB,EAChB,UAAuB,CAAC,CAAC,EACb;QACZ,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU;QACxC,MAAM,UAAkC;YACtC,gBAAgB;YAChB,GAAI,QAAQ,OAAO;QACrB;QAEA,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,QAAQ,aAAa,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,EAAE;QAChD;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC,GAAG,OAAO;gBACV;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC;wBACnD,SAAS,SAAS,MAAM,KAAK,MAAM,uBAAuB;wBAC1D,QAAQ,SAAS,MAAM;oBACzB,CAAC;gBAED,MAAM,QAAQ,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,EAAE;gBACpE,MAAc,MAAM,GAAG,SAAS,MAAM;gBACtC,MAAc,IAAI,GAAG,UAAU,IAAI;gBACrC,MAAM;YACR;YAEA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,wBAAwB;YACxB,IAAI,iBAAiB,aAAa,CAAC,UAAU,MAAM,EAAE;gBACnD,MAAM,eAAe,IAAI,MAAM;gBAC7B,aAAqB,MAAM,GAAG;gBAChC,MAAM;YACR;YACA,MAAM;QACR;IACF;IAEA,iBAAiB;IACjB,MAAM,MAAM,KAAa,EAAE,QAAgB,EAAyB;QAClE,OAAO,IAAI,CAAC,OAAO,CAAe,eAAe;YAC/C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAO;YAAS;QACzC;IACF;IAEA,MAAM,SAAS,IAKd,EAAyB;QACxB,OAAO,IAAI,CAAC,OAAO,CAAe,kBAAkB;YAClD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,aAA4B;QAChC,MAAM,WAAW,MAAM,IAAI,CAAC,OAAO,CAAiB;QACpD,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,cAAc,IAA0B,EAA4C;QACxF,OAAO,IAAI,CAAC,OAAO,CAAkC,iBAAiB;YACpE,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,iBAAiB;IACjB,MAAM,QACJ,UAAsB,CAAC,CAAC,EACxB,OAAO,CAAC,EACR,QAAQ,EAAE,EACa;QACvB,MAAM,SAAS,IAAI,gBAAgB;YACjC,MAAM,KAAK,QAAQ;YACnB,OAAO,MAAM,QAAQ;YACrB,GAAG,OAAO,WAAW,CACnB,OAAO,OAAO,CAAC,SAAS,MAAM,CAAC,CAAC,GAAG,MAAM,GAAK,UAAU,aAAa,UAAU,IAChF;QACH;QAEA,OAAO,IAAI,CAAC,OAAO,CAAe,CAAC,MAAM,EAAE,QAAQ;IACrD;IAEA,MAAM,OAAO,IAAY,EAA8C;QACrE,OAAO,IAAI,CAAC,OAAO,CAAoC,CAAC,MAAM,EAAE,MAAM;IACxE;IAEA,MAAM,kBAAkC;QACtC,IAAI;YACF,MAAM,SAAS,MAAM,IAAI,CAAC,OAAO,CAAkB;YACnD,OAAO,MAAM,OAAO,CAAC,OAAO,IAAI,IAAI,OAAO,IAAI,GAAG,EAAE;QACtD,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,kCAAkC;YAC/C,OAAO,EAAE;QACX;IACF;IAEA,MAAM,WAAW,KAAa,EAAE,WAAoB,EAA6D;QAC/G,OAAO,IAAI,CAAC,OAAO,CAAmD,CAAC,MAAM,EAAE,MAAM,MAAM,CAAC,EAAE;YAC5F,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE,SAAS;YAAY;QAC9C;IACF;IAEA,MAAM,oBAA6F;QACjG,OAAO,IAAI,CAAC,OAAO,CAAiE;IACtF;IAEA,MAAM,mBAAqE;QACzE,OAAO,IAAI,CAAC,OAAO,CAA0C;IAC/D;IAEA,iBAAiB;IACjB,MAAM,SAAS,OAAO,CAAC,EAAE,QAAQ,EAAE,EAA0B;QAC3D,MAAM,SAAS,IAAI,gBAAgB;YACjC,MAAM,KAAK,QAAQ;YACnB,OAAO,MAAM,QAAQ;QACvB;QAEA,OAAO,IAAI,CAAC,OAAO,CAAgB,CAAC,OAAO,EAAE,QAAQ;IACvD;IAEA,MAAM,QAAQ,IAAY,EAAqB;QAC7C,OAAO,IAAI,CAAC,OAAO,CAAW,CAAC,OAAO,EAAE,MAAM;IAChD;IAEA,cAAc;IACd,MAAM,WAAW,IAAU,EAAE,IAAkC,EAA4B;QACzF,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QAExB,MAAM,UAAkC,CAAC;QACzC,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,QAAQ,aAAa,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,EAAE;QAChD;QAEA,uCAAuC;QACvC,MAAM,WAAW,SAAS,WAAW,mBAAmB;QAExD,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU,EAAE;YACzD,QAAQ;YACR;YACA,MAAM;QACR;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,QAAQ,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC;oBAAE,SAAS;gBAAgB,CAAC;YAC7E,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,EAAE;QAC5D;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAElC,oEAAoE;QACpE,IAAI,SAAS,UAAU;YACrB,OAAO;gBAAE,KAAK,OAAO,MAAM;YAAC;QAC9B,OAAO;YACL,OAAO;gBAAE,KAAK,OAAO,cAAc;YAAC;QACtC;IACF;IAEA,eAAe;IACf,MAAM,kBAAkB,IAKvB,EAAkD;QACjD,OAAO,IAAI,CAAC,OAAO,CAAwC,YAAY;YACrE,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,qBAAqB;IACrB,MAAM,sBAAsB,KAAa,EAAoD;QAC3F,OAAO,IAAI,CAAC,OAAO,CAA0C,2BAA2B;YACtF,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAM;QAC/B;IACF;IAEA,MAAM,wBAAwB,KAAa,EAAoD;QAC7F,OAAO,IAAI,CAAC,qBAAqB,CAAC;IACpC;IAEA,MAAM,YAAY,KAAa,EAA0D;QACvF,OAAO,IAAI,CAAC,OAAO,CAAgD,sBAAsB;YACvF,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAM;QAC/B;IACF;IAEA,iBAAiB;IACjB,MAAM,eAAe,KAAa,EAAqD;QACrF,OAAO,IAAI,CAAC,OAAO,CAA2C,yBAAyB;YACrF,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAM;QAC/B;IACF;IAEA,MAAM,cAAc,KAAa,EAAE,QAAgB,EAA0D;QAC3G,OAAO,IAAI,CAAC,OAAO,CAAgD,wBAAwB;YACzF,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAO;YAAS;QACzC;IACF;AACF;AAGO,MAAM,MAAM,IAAI,UAAU;AAG1B,MAAM,eAAe,CAAC,KAAc,KAAc;IACvD,IAAI,YAAY,OAAO;IACvB,IAAI,CAAC,OAAO,CAAC,KAAK,OAAO;IACzB,IAAI,OAAO,KAAK,OAAO,CAAC,CAAC,EAAE,IAAI,cAAc,GAAG,IAAI,EAAE,IAAI,cAAc,IAAI;IAC5E,IAAI,KAAK,OAAO,CAAC,CAAC,EAAE,IAAI,cAAc,GAAG,CAAC,CAAC;IAC3C,IAAI,KAAK,OAAO,CAAC,OAAO,EAAE,IAAI,cAAc,IAAI;IAChD,OAAO;AACT;AAEO,MAAM,aAAa,CAAC;IACzB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;QACtD,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,MAAM,kBAAkB,CAAC;IAC9B,MAAM,SAAiC;QACrC,aAAa;QACb,aAAa;QACb,YAAY;QACZ,cAAc;QACd,iBAAiB;QACjB,aAAa;QACb,aAAa;QACb,YAAY;QACZ,aAAa;QACb,cAAc;IAChB;IACA,OAAO,MAAM,CAAC,KAAK,IAAI;AACzB;AAEO,MAAM,qBAAqB,CAAC;IACjC,MAAM,SAAiC;QACrC,WAAW;QACX,WAAW;QACX,mBAAmB;QACnB,oBAAoB;QACpB,sBAAsB;QACtB,uBAAuB;QACvB,mBAAmB;QACnB,iBAAiB;QACjB,oBAAoB;QACpB,SAAS;QACT,OAAO;QACP,UAAU;QACV,QAAQ;QACR,aAAa;IACf;IACA,OAAO,MAAM,CAAC,MAAM,IAAI;AAC1B;AAEO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,SAAiC;QACrC,UAAU;QACV,UAAU;QACV,UAAU;QACV,iBAAiB;QACjB,UAAU;QACV,UAAU;QACV,UAAU;IACZ;IACA,OAAO,MAAM,CAAC,KAAK,IAAI;AACzB", "debugId": null}}, {"offset": {"line": 418, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/job-portal/client/src/lib/error-handler.ts"], "sourcesContent": ["import { toast } from 'sonner'\n\nexport interface ApiError {\n  message: string\n  status?: number\n  code?: string\n}\n\nexport class AppError extends Error {\n  status: number\n  code?: string\n\n  constructor(message: string, status = 500, code?: string) {\n    super(message)\n    this.name = 'AppError'\n    this.status = status\n    this.code = code\n  }\n}\n\nexport const parseApiError = (error: any): ApiError => {\n  // If it's already an AppError, return it\n  if (error instanceof AppError) {\n    return {\n      message: error.message,\n      status: error.status,\n      code: error.code,\n    }\n  }\n\n  // If it's a standard Error with a message\n  if (error instanceof Error) {\n    return {\n      message: error.message,\n      status: 500,\n    }\n  }\n\n  // If it's an object with error details\n  if (typeof error === 'object' && error !== null) {\n    return {\n      message: error.message || error.error || 'An unexpected error occurred',\n      status: error.status || error.statusCode || 500,\n      code: error.code,\n    }\n  }\n\n  // If it's a string\n  if (typeof error === 'string') {\n    return {\n      message: error,\n      status: 500,\n    }\n  }\n\n  // Fallback\n  return {\n    message: 'An unexpected error occurred',\n    status: 500,\n  }\n}\n\nexport const handleApiError = (error: any, customMessage?: string): void => {\n  const apiError = parseApiError(error)\n\n  // Use custom message if provided, otherwise use the API error message\n  const message = customMessage || apiError.message\n  const status = apiError.status || 500\n\n  // Show different toast types based on status\n  if (status === 401) {\n    toast.error('Authentication required. Please log in.')\n  } else if (status === 403) {\n    toast.error(message)\n  } else if (status === 404) {\n    toast.error('The requested resource was not found.')\n  } else if (status === 422) {\n    toast.error('Please check your input and try again.')\n  } else if (status >= 500) {\n    toast.error('Server error. Please try again later.')\n  } else {\n    toast.error(message)\n  }\n\n  // Log error for debugging\n  console.error('API Error:', apiError)\n}\n\nexport const getErrorMessage = (error: any): string => {\n  const apiError = parseApiError(error)\n  return apiError.message\n}\n\nexport const isNetworkError = (error: any): boolean => {\n  return (\n    error instanceof TypeError ||\n    error.message?.includes('fetch') ||\n    error.message?.includes('network') ||\n    error.message?.includes('Network error')\n  )\n}\n\nexport const isAuthError = (error: any): boolean => {\n  const apiError = parseApiError(error)\n  const status = apiError.status || 500\n  return status === 401 || status === 403\n}\n\nexport const isValidationError = (error: any): boolean => {\n  const apiError = parseApiError(error)\n  const status = apiError.status || 500\n  return status === 422 || status === 400\n}\n\n// Email verification specific error handling\nexport const isEmailVerificationError = (error: any): boolean => {\n  const apiError = parseApiError(error)\n  const status = apiError.status || 500\n  return (\n    status === 403 &&\n    (apiError.message?.includes('verify') || apiError.message?.includes('verification'))\n  )\n}\n\nexport const handleEmailVerificationError = (error: any, userEmail?: string): void => {\n  if (isEmailVerificationError(error)) {\n    const apiError = parseApiError(error)\n    toast.error(apiError.message, {\n      duration: 6000,\n      action: userEmail ? {\n        label: 'Resend Email',\n        onClick: () => {\n          // This will be handled by the component\n          window.dispatchEvent(new CustomEvent('resend-verification', { detail: { email: userEmail } }))\n        }\n      } : undefined\n    })\n  } else {\n    handleApiError(error)\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;AAQO,MAAM,iBAAiB;IAC5B,OAAc;IACd,KAAa;IAEb,YAAY,OAAe,EAAE,SAAS,GAAG,EAAE,IAAa,CAAE;QACxD,KAAK,CAAC;QACN,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAEO,MAAM,gBAAgB,CAAC;IAC5B,yCAAyC;IACzC,IAAI,iBAAiB,UAAU;QAC7B,OAAO;YACL,SAAS,MAAM,OAAO;YACtB,QAAQ,MAAM,MAAM;YACpB,MAAM,MAAM,IAAI;QAClB;IACF;IAEA,0CAA0C;IAC1C,IAAI,iBAAiB,OAAO;QAC1B,OAAO;YACL,SAAS,MAAM,OAAO;YACtB,QAAQ;QACV;IACF;IAEA,uCAAuC;IACvC,IAAI,OAAO,UAAU,YAAY,UAAU,MAAM;QAC/C,OAAO;YACL,SAAS,MAAM,OAAO,IAAI,MAAM,KAAK,IAAI;YACzC,QAAQ,MAAM,MAAM,IAAI,MAAM,UAAU,IAAI;YAC5C,MAAM,MAAM,IAAI;QAClB;IACF;IAEA,mBAAmB;IACnB,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO;YACL,SAAS;YACT,QAAQ;QACV;IACF;IAEA,WAAW;IACX,OAAO;QACL,SAAS;QACT,QAAQ;IACV;AACF;AAEO,MAAM,iBAAiB,CAAC,OAAY;IACzC,MAAM,WAAW,cAAc;IAE/B,sEAAsE;IACtE,MAAM,UAAU,iBAAiB,SAAS,OAAO;IACjD,MAAM,SAAS,SAAS,MAAM,IAAI;IAElC,6CAA6C;IAC7C,IAAI,WAAW,KAAK;QAClB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;IACd,OAAO,IAAI,WAAW,KAAK;QACzB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;IACd,OAAO,IAAI,WAAW,KAAK;QACzB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;IACd,OAAO,IAAI,WAAW,KAAK;QACzB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;IACd,OAAO,IAAI,UAAU,KAAK;QACxB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;IACd,OAAO;QACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;IACd;IAEA,0BAA0B;IAC1B,QAAQ,KAAK,CAAC,cAAc;AAC9B;AAEO,MAAM,kBAAkB,CAAC;IAC9B,MAAM,WAAW,cAAc;IAC/B,OAAO,SAAS,OAAO;AACzB;AAEO,MAAM,iBAAiB,CAAC;IAC7B,OACE,iBAAiB,aACjB,MAAM,OAAO,EAAE,SAAS,YACxB,MAAM,OAAO,EAAE,SAAS,cACxB,MAAM,OAAO,EAAE,SAAS;AAE5B;AAEO,MAAM,cAAc,CAAC;IAC1B,MAAM,WAAW,cAAc;IAC/B,MAAM,SAAS,SAAS,MAAM,IAAI;IAClC,OAAO,WAAW,OAAO,WAAW;AACtC;AAEO,MAAM,oBAAoB,CAAC;IAChC,MAAM,WAAW,cAAc;IAC/B,MAAM,SAAS,SAAS,MAAM,IAAI;IAClC,OAAO,WAAW,OAAO,WAAW;AACtC;AAGO,MAAM,2BAA2B,CAAC;IACvC,MAAM,WAAW,cAAc;IAC/B,MAAM,SAAS,SAAS,MAAM,IAAI;IAClC,OACE,WAAW,OACX,CAAC,SAAS,OAAO,EAAE,SAAS,aAAa,SAAS,OAAO,EAAE,SAAS,eAAe;AAEvF;AAEO,MAAM,+BAA+B,CAAC,OAAY;IACvD,IAAI,yBAAyB,QAAQ;QACnC,MAAM,WAAW,cAAc;QAC/B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,SAAS,OAAO,EAAE;YAC5B,UAAU;YACV,QAAQ,YAAY;gBAClB,OAAO;gBACP,SAAS;oBACP,wCAAwC;oBACxC,OAAO,aAAa,CAAC,IAAI,YAAY,uBAAuB;wBAAE,QAAQ;4BAAE,OAAO;wBAAU;oBAAE;gBAC7F;YACF,IAAI;QACN;IACF,OAAO;QACL,eAAe;IACjB;AACF", "debugId": null}}, {"offset": {"line": 549, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/job-portal/client/src/hooks/use-auth.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useEffect, useState, ReactNode } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { api, User, AuthResponse } from '@/lib/api'\nimport { toast } from 'sonner'\nimport { handleApiError, handleEmailVerificationError, isEmailVerificationError } from '@/lib/error-handler'\n\ninterface RegisterData {\n  email: string\n  password: string\n  phone?: string\n  experienceLevel?: string\n}\n\ninterface AuthContextType {\n  user: User | null\n  loading: boolean\n  isAuthenticated: boolean\n  login: (token: string, user: User) => void\n  loginWithCredentials: (email: string, password: string) => Promise<void>\n  register: (data: RegisterData) => Promise<void>\n  resendVerificationEmail: (email: string) => Promise<void>\n  logout: () => void\n  refreshUser: () => Promise<void>\n  updateUserProfile: (updates: Partial<User>) => void\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function AuthProvider({ children }: { children: ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [loading, setLoading] = useState(true)\n  const router = useRouter()\n\n  const isAuthenticated = !!user\n\n  const login = (token: string, userData: User) => {\n    localStorage.setItem('auth_token', token)\n    api.setToken(token)\n    setUser(userData)\n    setLoading(false)\n  }\n\n  const loginWithCredentials = async (email: string, password: string) => {\n    try {\n      setLoading(true)\n      const response: AuthResponse = await api.login(email, password)\n\n      login(response.token, response.user)\n      toast.success('Welcome back!')\n\n      // Redirect based on profile completion\n      if (!response.user.profileCompleted) {\n        router.push('/dashboard/profile?welcome=true')\n      } else {\n        router.push('/dashboard/profile')\n      }\n    } catch (error) {\n      console.error('Login failed:', error)\n\n      // Handle email verification errors specially\n      if (isEmailVerificationError(error)) {\n        handleEmailVerificationError(error, email)\n      } else {\n        handleApiError(error, 'Login failed')\n      }\n\n      throw error\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const register = async (data: RegisterData) => {\n    try {\n      setLoading(true)\n      const response: any = await api.register(data)\n\n      // Check if email verification is required\n      if (response.requiresVerification) {\n        toast.success('Account created! Please check your email to verify your account. 📧')\n        router.push('/auth/verify-email-sent?email=' + encodeURIComponent(data.email))\n      } else {\n        // Old flow - direct login (fallback)\n        login(response.token, response.user)\n        toast.success('Account created successfully!')\n        router.push('/dashboard/profile?welcome=true')\n      }\n    } catch (error) {\n      console.error('Registration failed:', error)\n      handleApiError(error, 'Registration failed')\n      throw error\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const resendVerificationEmail = async (email: string) => {\n    try {\n      setLoading(true)\n      await api.resendVerificationEmail(email)\n      toast.success('Verification email sent! Please check your inbox.')\n    } catch (error) {\n      console.error('Failed to resend verification email:', error)\n      handleApiError(error, 'Failed to resend verification email')\n      throw error\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const logout = () => {\n    localStorage.removeItem('auth_token')\n    api.clearToken()\n    setUser(null)\n    setLoading(false)\n    toast.success('Logged out successfully')\n    router.push('/')\n  }\n\n  const refreshUser = async () => {\n    try {\n      setLoading(true)\n      const userData = await api.getProfile()\n      setUser(userData)\n    } catch (error: any) {\n      console.error('Failed to fetch user profile:', error)\n      if (error.message?.includes('401') || error.message?.includes('Unauthorized')) {\n        logout()\n      }\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const updateUserProfile = (updates: Partial<User>) => {\n    if (user) {\n      setUser({ ...user, ...updates })\n    }\n  }\n\n  useEffect(() => {\n    const initAuth = async () => {\n      const token = localStorage.getItem('auth_token')\n      console.log('🔍 InitAuth - Token found:', !!token)\n\n      if (token) {\n        api.setToken(token)\n        try {\n          setLoading(true)\n          console.log('🔄 Fetching user profile...')\n          const userData = await api.getProfile()\n          console.log('✅ User profile fetched:', userData)\n          setUser(userData)\n        } catch (error: any) {\n          console.error('❌ Failed to fetch user profile:', error)\n          if (error.message?.includes('401') || error.message?.includes('Unauthorized')) {\n            console.log('🔑 Token expired, clearing auth')\n            localStorage.removeItem('auth_token')\n            api.clearToken()\n            setUser(null)\n          }\n        } finally {\n          setLoading(false)\n        }\n      } else {\n        console.log('🚫 No token found, setting loading to false')\n        setLoading(false)\n      }\n    }\n\n    initAuth()\n  }, [])\n\n  const value = {\n    user,\n    loading,\n    isAuthenticated,\n    login,\n    loginWithCredentials,\n    register,\n    resendVerificationEmail,\n    logout,\n    refreshUser,\n    updateUserProfile,\n  }\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n\nexport function useRequireAuth() {\n  const auth = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!auth.loading && !auth.isAuthenticated) {\n      router.push('/auth/login')\n    }\n  }, [auth.loading, auth.isAuthenticated, router])\n\n  return auth\n}\n\n// Hook for redirecting authenticated users\nexport function useRedirectIfAuthenticated() {\n  const { user, loading } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!loading && user) {\n      router.push('/dashboard/profile')\n    }\n  }, [user, loading, router])\n\n  return { user, loading }\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AA4BA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAA2B;IAChE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,kBAAkB,CAAC,CAAC;IAE1B,MAAM,QAAQ,CAAC,OAAe;QAC5B,aAAa,OAAO,CAAC,cAAc;QACnC,iHAAA,CAAA,MAAG,CAAC,QAAQ,CAAC;QACb,QAAQ;QACR,WAAW;IACb;IAEA,MAAM,uBAAuB,OAAO,OAAe;QACjD,IAAI;YACF,WAAW;YACX,MAAM,WAAyB,MAAM,iHAAA,CAAA,MAAG,CAAC,KAAK,CAAC,OAAO;YAEtD,MAAM,SAAS,KAAK,EAAE,SAAS,IAAI;YACnC,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAEd,uCAAuC;YACvC,IAAI,CAAC,SAAS,IAAI,CAAC,gBAAgB,EAAE;gBACnC,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAE/B,6CAA6C;YAC7C,IAAI,CAAA,GAAA,8HAAA,CAAA,2BAAwB,AAAD,EAAE,QAAQ;gBACnC,CAAA,GAAA,8HAAA,CAAA,+BAA4B,AAAD,EAAE,OAAO;YACtC,OAAO;gBACL,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD,EAAE,OAAO;YACxB;YAEA,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,IAAI;YACF,WAAW;YACX,MAAM,WAAgB,MAAM,iHAAA,CAAA,MAAG,CAAC,QAAQ,CAAC;YAEzC,0CAA0C;YAC1C,IAAI,SAAS,oBAAoB,EAAE;gBACjC,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO,IAAI,CAAC,mCAAmC,mBAAmB,KAAK,KAAK;YAC9E,OAAO;gBACL,qCAAqC;gBACrC,MAAM,SAAS,KAAK,EAAE,SAAS,IAAI;gBACnC,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO,IAAI,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD,EAAE,OAAO;YACtB,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,0BAA0B,OAAO;QACrC,IAAI;YACF,WAAW;YACX,MAAM,iHAAA,CAAA,MAAG,CAAC,uBAAuB,CAAC;YAClC,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD,EAAE,OAAO;YACtB,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,SAAS;QACb,aAAa,UAAU,CAAC;QACxB,iHAAA,CAAA,MAAG,CAAC,UAAU;QACd,QAAQ;QACR,WAAW;QACX,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QACd,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,cAAc;QAClB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,iHAAA,CAAA,MAAG,CAAC,UAAU;YACrC,QAAQ;QACV,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,IAAI,MAAM,OAAO,EAAE,SAAS,UAAU,MAAM,OAAO,EAAE,SAAS,iBAAiB;gBAC7E;YACF;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,MAAM;YACR,QAAQ;gBAAE,GAAG,IAAI;gBAAE,GAAG,OAAO;YAAC;QAChC;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW;YACf,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,QAAQ,GAAG,CAAC,8BAA8B,CAAC,CAAC;YAE5C,IAAI,OAAO;gBACT,iHAAA,CAAA,MAAG,CAAC,QAAQ,CAAC;gBACb,IAAI;oBACF,WAAW;oBACX,QAAQ,GAAG,CAAC;oBACZ,MAAM,WAAW,MAAM,iHAAA,CAAA,MAAG,CAAC,UAAU;oBACrC,QAAQ,GAAG,CAAC,2BAA2B;oBACvC,QAAQ;gBACV,EAAE,OAAO,OAAY;oBACnB,QAAQ,KAAK,CAAC,mCAAmC;oBACjD,IAAI,MAAM,OAAO,EAAE,SAAS,UAAU,MAAM,OAAO,EAAE,SAAS,iBAAiB;wBAC7E,QAAQ,GAAG,CAAC;wBACZ,aAAa,UAAU,CAAC;wBACxB,iHAAA,CAAA,MAAG,CAAC,UAAU;wBACd,QAAQ;oBACV;gBACF,SAAU;oBACR,WAAW;gBACb;YACF,OAAO;gBACL,QAAQ,GAAG,CAAC;gBACZ,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBAAO,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAC9C;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEO,SAAS;IACd,MAAM,OAAO;IACb,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,KAAK,OAAO,IAAI,CAAC,KAAK,eAAe,EAAE;YAC1C,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC,KAAK,OAAO;QAAE,KAAK,eAAe;QAAE;KAAO;IAE/C,OAAO;AACT;AAGO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG;IAC1B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,MAAM;YACpB,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,OAAO;QAAE;QAAM;IAAQ;AACzB", "debugId": null}}, {"offset": {"line": 765, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/job-portal/client/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport const formatDate = (dateString: string): string => {\n  const date = new Date(dateString)\n  return date.toLocaleDateString(\"en-US\", {\n    year: \"numeric\",\n    month: \"short\",\n    day: \"numeric\"\n  })\n}\n\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,MAAM,aAAa,CAAC;IACzB,MAAM,OAAO,IAAI,KAAK;IACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF", "debugId": null}}, {"offset": {"line": 790, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/job-portal/client/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 847, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/job-portal/client/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,8OAAC,4KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,8OAAC,4KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,8OAAC,4KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,8OAAC,4KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1109, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/job-portal/client/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,kKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1161, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/job-portal/client/src/components/layout/header.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { useAuth } from '@/hooks/use-auth'\nimport { Button } from '@/components/ui/button'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu'\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'\nimport {\n  User,\n  LogOut,\n  FileText,\n  Menu,\n  Briefcase,\n  BookOpen,\n  Phone,\n  Info\n} from 'lucide-react'\nimport { useState } from 'react'\n\nexport function Header() {\n  const { user, logout, isAuthenticated } = useAuth()\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)\n\n  const navigation = [\n    { name: 'Jobs', href: '/jobs', icon: Briefcase },\n    { name: 'Blogs', href: '/blogs', icon: BookOpen },\n    { name: 'About', href: '/about', icon: Info },\n    { name: 'Contact', href: '/contact', icon: Phone },\n  ]\n\n  return (\n    <header className=\"sticky top-0 z-50 w-full glass border-b border-border/50\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex h-20 items-center justify-between\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"flex items-center space-x-3 group\">\n              <div className=\"h-10 w-10 rounded-lg gradient-primary flex items-center justify-center shadow-md transition-all duration-300 group-hover:shadow-lg\">\n                <Briefcase className=\"h-6 w-6 text-primary-foreground\" />\n              </div>\n              <span className=\"text-2xl font-bold text-foreground tracking-tight\">\n                Job<span className=\"text-primary\">Portal</span>\n              </span>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex items-center space-x-2\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className=\"px-4 py-2 text-sm font-semibold text-muted-foreground hover:text-foreground hover:bg-muted/50 rounded-full transition-all duration-200 hover:scale-105\"\n              >\n                {item.name}\n              </Link>\n            ))}\n          </nav>\n\n          {/* Auth Section */}\n          <div className=\"flex items-center space-x-4\">\n            {isAuthenticated ? (\n              <DropdownMenu>\n                <DropdownMenuTrigger asChild>\n                  <Button variant=\"ghost\" className=\"relative h-8 w-8 rounded-full\">\n                    <Avatar className=\"h-8 w-8\">\n                      <AvatarImage\n                        src={user?.profilePicture}\n                        alt={user?.email}\n                      />\n                      <AvatarFallback>\n                        {user?.email?.charAt(0).toUpperCase()}\n                      </AvatarFallback>\n                    </Avatar>\n                  </Button>\n                </DropdownMenuTrigger>\n                <DropdownMenuContent className=\"w-56\" align=\"end\" forceMount>\n                  <div className=\"flex items-center justify-start gap-2 p-2\">\n                    <div className=\"flex flex-col space-y-1 leading-none\">\n                      <p className=\"font-medium\">{user?.email}</p>\n                      {user?.firstName && (\n                        <p className=\"text-xs text-muted-foreground\">\n                          {user.firstName} {user.lastName}\n                        </p>\n                      )}\n                    </div>\n                  </div>\n                  <DropdownMenuSeparator />\n                  <DropdownMenuItem asChild>\n                    <Link href=\"/dashboard/profile\" className=\"flex items-center\">\n                      <User className=\"mr-2 h-4 w-4\" />\n                      Profile\n                    </Link>\n                  </DropdownMenuItem>\n                  <DropdownMenuItem asChild>\n                    <Link href=\"/dashboard/applications\" className=\"flex items-center\">\n                      <FileText className=\"mr-2 h-4 w-4\" />\n                      My Applications\n                    </Link>\n                  </DropdownMenuItem>\n                  <DropdownMenuSeparator />\n                  <DropdownMenuItem onClick={logout} className=\"flex items-center\">\n                    <LogOut className=\"mr-2 h-4 w-4\" />\n                    Log out\n                  </DropdownMenuItem>\n                </DropdownMenuContent>\n              </DropdownMenu>\n            ) : (\n              <div className=\"hidden md:flex items-center space-x-3\">\n                <Button variant=\"ghost\" asChild className=\"rounded-lg font-semibold hover:bg-muted/50\">\n                  <Link href=\"/auth/login\">Sign In</Link>\n                </Button>\n                <Button asChild className=\"rounded-lg font-semibold gradient-primary shadow-md hover:shadow-lg transition-all duration-200\">\n                  <Link href=\"/auth/register\">Sign Up</Link>\n                </Button>\n              </div>\n            )}\n\n            {/* Mobile menu button */}\n            <Button\n              variant=\"ghost\"\n              className=\"md:hidden rounded-full h-10 w-10 p-0\"\n              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}\n            >\n              <Menu className=\"h-5 w-5\" />\n            </Button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {mobileMenuOpen && (\n          <div className=\"md:hidden\">\n            <div className=\"px-4 pt-4 pb-6 space-y-3 border-t border-border/50 glass\">\n              {navigation.map((item) => {\n                const Icon = item.icon\n                return (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className=\"flex items-center px-4 py-3 text-base font-semibold text-muted-foreground hover:text-foreground hover:bg-muted/50 rounded-2xl transition-all duration-200\"\n                    onClick={() => setMobileMenuOpen(false)}\n                  >\n                    <Icon className=\"mr-3 h-5 w-5\" />\n                    {item.name}\n                  </Link>\n                )\n              })}\n\n              {!isAuthenticated && (\n                <div className=\"pt-4 border-t border-border/50 space-y-3\">\n                  <Button variant=\"ghost\" asChild className=\"w-full justify-start rounded-2xl font-semibold\">\n                    <Link href=\"/auth/login\" onClick={() => setMobileMenuOpen(false)}>\n                      Sign In\n                    </Link>\n                  </Button>\n                  <Button asChild className=\"w-full rounded-2xl font-semibold gradient-primary\">\n                    <Link href=\"/auth/register\" onClick={() => setMobileMenuOpen(false)}>\n                      Sign Up\n                    </Link>\n                  </Button>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAOA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AAvBA;;;;;;;;;AAyBO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,UAAO,AAAD;IAChD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,aAAa;QACjB;YAAE,MAAM;YAAQ,MAAM;YAAS,MAAM,4MAAA,CAAA,YAAS;QAAC;QAC/C;YAAE,MAAM;YAAS,MAAM;YAAU,MAAM,8MAAA,CAAA,WAAQ;QAAC;QAChD;YAAE,MAAM;YAAS,MAAM;YAAU,MAAM,kMAAA,CAAA,OAAI;QAAC;QAC5C;YAAE,MAAM;YAAW,MAAM;YAAY,MAAM,oMAAA,CAAA,QAAK;QAAC;KAClD;IAED,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,4MAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;kDAEvB,8OAAC;wCAAK,WAAU;;4CAAoD;0DAC/D,8OAAC;gDAAK,WAAU;0DAAe;;;;;;;;;;;;;;;;;;;;;;;sCAMxC,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;8CAET,KAAK,IAAI;mCAJL,KAAK,IAAI;;;;;;;;;;sCAUpB,8OAAC;4BAAI,WAAU;;gCACZ,gCACC,8OAAC,4IAAA,CAAA,eAAY;;sDACX,8OAAC,4IAAA,CAAA,sBAAmB;4CAAC,OAAO;sDAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,WAAU;0DAChC,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDAAC,WAAU;;sEAChB,8OAAC,kIAAA,CAAA,cAAW;4DACV,KAAK,MAAM;4DACX,KAAK,MAAM;;;;;;sEAEb,8OAAC,kIAAA,CAAA,iBAAc;sEACZ,MAAM,OAAO,OAAO,GAAG;;;;;;;;;;;;;;;;;;;;;;sDAKhC,8OAAC,4IAAA,CAAA,sBAAmB;4CAAC,WAAU;4CAAO,OAAM;4CAAM,UAAU;;8DAC1D,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAAe,MAAM;;;;;;4DACjC,MAAM,2BACL,8OAAC;gEAAE,WAAU;;oEACV,KAAK,SAAS;oEAAC;oEAAE,KAAK,QAAQ;;;;;;;;;;;;;;;;;;8DAKvC,8OAAC,4IAAA,CAAA,wBAAqB;;;;;8DACtB,8OAAC,4IAAA,CAAA,mBAAgB;oDAAC,OAAO;8DACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAqB,WAAU;;0EACxC,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;8DAIrC,8OAAC,4IAAA,CAAA,mBAAgB;oDAAC,OAAO;8DACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAA0B,WAAU;;0EAC7C,8OAAC,8MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;8DAIzC,8OAAC,4IAAA,CAAA,wBAAqB;;;;;8DACtB,8OAAC,4IAAA,CAAA,mBAAgB;oDAAC,SAAS;oDAAQ,WAAU;;sEAC3C,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;yDAMzC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,OAAO;4CAAC,WAAU;sDACxC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;0DAAc;;;;;;;;;;;sDAE3B,8OAAC,kIAAA,CAAA,SAAM;4CAAC,OAAO;4CAAC,WAAU;sDACxB,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;0DAAiB;;;;;;;;;;;;;;;;;8CAMlC,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;oCACV,SAAS,IAAM,kBAAkB,CAAC;8CAElC,cAAA,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;gBAMrB,gCACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;4BACZ,WAAW,GAAG,CAAC,CAAC;gCACf,MAAM,OAAO,KAAK,IAAI;gCACtB,qBACE,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,SAAS,IAAM,kBAAkB;;sDAEjC,8OAAC;4CAAK,WAAU;;;;;;wCACf,KAAK,IAAI;;mCANL,KAAK,IAAI;;;;;4BASpB;4BAEC,CAAC,iCACA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,OAAO;wCAAC,WAAU;kDACxC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAc,SAAS,IAAM,kBAAkB;sDAAQ;;;;;;;;;;;kDAIpE,8OAAC,kIAAA,CAAA,SAAM;wCAAC,OAAO;wCAAC,WAAU;kDACxB,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAiB,SAAS,IAAM,kBAAkB;sDAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYzF", "debugId": null}}, {"offset": {"line": 1630, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/job-portal/client/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1727, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/job-portal/client/src/components/error-boundary.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { AlertTriangle, RefreshCw } from 'lucide-react'\n\ninterface ErrorBoundaryState {\n  hasError: boolean\n  error?: Error\n}\n\ninterface ErrorBoundaryProps {\n  children: React.ReactNode\n  fallback?: React.ComponentType<{ error?: Error; resetError: () => void }>\n}\n\nexport class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {\n  constructor(props: ErrorBoundaryProps) {\n    super(props)\n    this.state = { hasError: false }\n  }\n\n  static getDerivedStateFromError(error: Error): ErrorBoundaryState {\n    return { hasError: true, error }\n  }\n\n  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {\n    console.error('Error caught by boundary:', error, errorInfo)\n  }\n\n  resetError = () => {\n    this.setState({ hasError: false, error: undefined })\n  }\n\n  render() {\n    if (this.state.hasError) {\n      if (this.props.fallback) {\n        const FallbackComponent = this.props.fallback\n        return <FallbackComponent error={this.state.error} resetError={this.resetError} />\n      }\n\n      return <DefaultErrorFallback error={this.state.error} resetError={this.resetError} />\n    }\n\n    return this.props.children\n  }\n}\n\ninterface ErrorFallbackProps {\n  error?: Error\n  resetError: () => void\n}\n\nfunction DefaultErrorFallback({ error, resetError }: ErrorFallbackProps) {\n  return (\n    <div className=\"min-h-screen flex items-center justify-center p-4\">\n      <Card className=\"w-full max-w-md\">\n        <CardHeader className=\"text-center\">\n          <div className=\"mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100\">\n            <AlertTriangle className=\"h-6 w-6 text-red-600\" />\n          </div>\n          <CardTitle className=\"text-xl\">Something went wrong</CardTitle>\n          <CardDescription>\n            {error?.message || 'An unexpected error occurred. Please try again.'}\n          </CardDescription>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <Button onClick={resetError} className=\"w-full\">\n            <RefreshCw className=\"mr-2 h-4 w-4\" />\n            Try Again\n          </Button>\n          <Button \n            variant=\"outline\" \n            onClick={() => window.location.href = '/'} \n            className=\"w-full\"\n          >\n            Go Home\n          </Button>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n\n// Hook for error handling in functional components\nexport function useErrorHandler() {\n  return (error: Error, errorInfo?: { componentStack: string }) => {\n    console.error('Error caught:', error, errorInfo)\n    // You can add error reporting service here\n  }\n}\n\n// Higher-order component for wrapping components with error boundary\nexport function withErrorBoundary<P extends object>(\n  Component: React.ComponentType<P>,\n  fallback?: React.ComponentType<ErrorFallbackProps>\n) {\n  const WrappedComponent = (props: P) => (\n    <ErrorBoundary fallback={fallback}>\n      <Component {...props} />\n    </ErrorBoundary>\n  )\n\n  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`\n  return WrappedComponent\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AACA;AAAA;AALA;;;;;;AAiBO,MAAM,sBAAsB,qMAAA,CAAA,UAAK,CAAC,SAAS;IAChD,YAAY,KAAyB,CAAE;QACrC,KAAK,CAAC;QACN,IAAI,CAAC,KAAK,GAAG;YAAE,UAAU;QAAM;IACjC;IAEA,OAAO,yBAAyB,KAAY,EAAsB;QAChE,OAAO;YAAE,UAAU;YAAM;QAAM;IACjC;IAEA,kBAAkB,KAAY,EAAE,SAA0B,EAAE;QAC1D,QAAQ,KAAK,CAAC,6BAA6B,OAAO;IACpD;IAEA,aAAa;QACX,IAAI,CAAC,QAAQ,CAAC;YAAE,UAAU;YAAO,OAAO;QAAU;IACpD,EAAC;IAED,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACvB,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;gBACvB,MAAM,oBAAoB,IAAI,CAAC,KAAK,CAAC,QAAQ;gBAC7C,qBAAO,8OAAC;oBAAkB,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;oBAAE,YAAY,IAAI,CAAC,UAAU;;;;;;YAChF;YAEA,qBAAO,8OAAC;gBAAqB,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;gBAAE,YAAY,IAAI,CAAC,UAAU;;;;;;QACnF;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;AACF;AAOA,SAAS,qBAAqB,EAAE,KAAK,EAAE,UAAU,EAAsB;IACrE,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,8OAAC,gIAAA,CAAA,aAAU;oBAAC,WAAU;;sCACpB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;;;;;;sCAE3B,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;sCAAU;;;;;;sCAC/B,8OAAC,gIAAA,CAAA,kBAAe;sCACb,OAAO,WAAW;;;;;;;;;;;;8BAGvB,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAS;4BAAY,WAAU;;8CACrC,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAGxC,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;4BACtC,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOX;AAGO,SAAS;IACd,OAAO,CAAC,OAAc;QACpB,QAAQ,KAAK,CAAC,iBAAiB,OAAO;IACtC,2CAA2C;IAC7C;AACF;AAGO,SAAS,kBACd,SAAiC,EACjC,QAAkD;IAElD,MAAM,mBAAmB,CAAC,sBACxB,8OAAC;YAAc,UAAU;sBACvB,cAAA,8OAAC;gBAAW,GAAG,KAAK;;;;;;;;;;;IAIxB,iBAAiB,WAAW,GAAG,CAAC,kBAAkB,EAAE,UAAU,WAAW,IAAI,UAAU,IAAI,CAAC,CAAC,CAAC;IAC9F,OAAO;AACT", "debugId": null}}]}