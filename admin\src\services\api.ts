const API_BASE_URL = 'http://localhost:5000/api'

// Helper function to get auth headers
const getAuthHeaders = () => {
  const token = localStorage.getItem('admin_token')
  return {
    'Content-Type': 'application/json',
    ...(token && { Authorization: `Bearer ${token}` }),
  }
}

// Generic API error handler
export class ApiError extends Error {
  status: number
  data?: any

  constructor(status: number, message: string, data?: any) {
    super(message)
    this.name = 'ApiError'
    this.status = status
    this.data = data
  }
}

// Generic fetch wrapper
const apiRequest = async <T>(endpoint: string, options: RequestInit = {}): Promise<T> => {
  const url = `${API_BASE_URL}${endpoint}`
  const config: RequestInit = {
    headers: getAuthHeaders(),
    ...options,
  }

  const response = await fetch(url, config)

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}))
    throw new ApiError(response.status, errorData.message || 'An error occurred', errorData)
  }

  return response.json()
}

// Auth-specific fetch wrapper (no auth headers)
const authRequest = async <T>(endpoint: string, options: RequestInit = {}): Promise<T> => {
  const url = `${API_BASE_URL}${endpoint}`
  const config: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
    },
    ...options,
  }

  const response = await fetch(url, config)

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}))
    throw new ApiError(response.status, errorData.message || 'An error occurred', errorData)
  }

  return response.json()
}

// Dashboard API
export const dashboardApi = {
  getStats: () => apiRequest<any>('/admin/dashboard/stats'),
}

// Jobs API
export const jobsApi = {
  getJobs: (params: Record<string, any>) => {
    const searchParams = new URLSearchParams()
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        searchParams.append(key, String(value))
      }
    })
    return apiRequest<any>(`/admin/jobs?${searchParams}`)
  },
  getJob: (id: string) => apiRequest<any>(`/admin/jobs/${id}`),
  createJob: (data: any) => apiRequest<any>('/admin/jobs', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  updateJob: (id: string, data: any) => apiRequest<any>(`/admin/jobs/${id}`, {
    method: 'PUT',
    body: JSON.stringify(data),
  }),
  deleteJob: (id: string) => apiRequest<any>(`/admin/jobs/${id}`, {
    method: 'DELETE',
  }),
  getCategories: () => apiRequest<{ categories: string[]; total: number }>('/jobs/categories'),
}

// Users API
export const usersApi = {
  getUsers: (params: Record<string, any>) => {
    const searchParams = new URLSearchParams()
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        searchParams.append(key, String(value))
      }
    })
    return apiRequest<any>(`/admin/users?${searchParams}`)
  },
  getUser: (id: string) => apiRequest<any>(`/admin/users/${id}/profile`),
  deleteUser: (id: string) => apiRequest<any>(`/admin/users/${id}`, {
    method: 'DELETE',
  }),
}

// Applications API
export const applicationsApi = {
  getApplications: (params: Record<string, any>) => {
    const searchParams = new URLSearchParams()
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        searchParams.append(key, String(value))
      }
    })
    return apiRequest<any>(`/admin/applications?${searchParams}`)
  },
  updateApplicationStatus: (id: string, status: string) => apiRequest<any>(`/admin/applications/${id}/status`, {
    method: 'PUT',
    body: JSON.stringify({ status }),
  }),
}

// Blogs API
export const blogsApi = {
  getBlogs: (params: Record<string, any>) => {
    const searchParams = new URLSearchParams()
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        searchParams.append(key, String(value))
      }
    })
    return apiRequest<any>(`/admin/blogs?${searchParams}`)
  },
  getBlog: (id: string) => apiRequest<any>(`/admin/blogs/${id}`),
  createBlog: (data: any) => apiRequest<any>('/admin/blogs', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  updateBlog: (id: string, data: any) => apiRequest<any>(`/admin/blogs/${id}`, {
    method: 'PUT',
    body: JSON.stringify(data),
  }),
  deleteBlog: (id: string) => apiRequest<any>(`/admin/blogs/${id}`, {
    method: 'DELETE',
  }),
}

// Upload API - separate function to handle file uploads without JSON headers
const uploadRequest = async <T>(endpoint: string, file: File): Promise<T> => {
  const url = `${API_BASE_URL}${endpoint}`
  const token = localStorage.getItem('admin_token')

  const formData = new FormData()
  formData.append('file', file)

  const config: RequestInit = {
    method: 'POST',
    body: formData,
    headers: {
      ...(token && { Authorization: `Bearer ${token}` }),
      // Don't set Content-Type - let browser set it with boundary for FormData
    },
  }

  const response = await fetch(url, config)

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}))
    throw new ApiError(response.status, errorData.message || 'Upload failed', errorData)
  }

  return response.json()
}

export const uploadApi = {
  uploadBlogImage: (file: File) => uploadRequest<any>('/upload/blog-image', file),
  uploadCompanyLogo: (file: File) => uploadRequest<any>('/upload/company-logo', file),
}

// Auth API
export const authApi = {
  register: (data: any) => authRequest<any>('/auth/admin/register', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  login: (data: any) => authRequest<any>('/auth/admin/login', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  getProfile: () => apiRequest<any>('/auth/admin/profile'),
}
