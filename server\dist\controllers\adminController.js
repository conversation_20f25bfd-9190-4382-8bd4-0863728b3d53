"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.adminController = void 0;
const db_1 = require("../lib/db");
const errorHandler_1 = require("../middleware/errorHandler");
const utils_1 = require("../lib/utils");
const job_1 = require("../schemas/job");
const blog_1 = require("../schemas/blog");
const admin_1 = require("../schemas/admin");
exports.adminController = {
    getDashboardStats: async (req, res, next) => {
        try {
            const [totalJobs, activeJobs, totalUsers, totalApplications, pendingApplications, totalBlogs, publishedBlogs, recentApplications] = await Promise.all([
                db_1.db.job.count(),
                db_1.db.job.count({ where: { isActive: true } }),
                db_1.db.user.count({ where: { role: 'USER' } }),
                db_1.db.application.count(),
                db_1.db.application.count({ where: { status: 'PENDING' } }),
                db_1.db.blog.count(),
                db_1.db.blog.count({ where: { isPublished: true } }),
                db_1.db.application.findMany({
                    take: 5,
                    orderBy: { appliedAt: 'desc' },
                    include: {
                        user: {
                            select: {
                                firstName: true,
                                lastName: true,
                                email: true
                            }
                        },
                        job: {
                            select: {
                                title: true,
                                companyName: true
                            }
                        }
                    }
                })
            ]);
            const applicationRate = totalJobs > 0 ? (totalApplications / totalJobs).toFixed(1) : '0';
            const activeJobsPercentage = totalJobs > 0 ? ((activeJobs / totalJobs) * 100).toFixed(1) : '0';
            const stats = {
                overview: {
                    totalJobs,
                    activeJobs,
                    totalUsers,
                    totalApplications,
                    pendingApplications,
                    totalBlogs,
                    publishedBlogs
                },
                metrics: {
                    applicationRate: parseFloat(applicationRate),
                    activeJobsPercentage: parseFloat(activeJobsPercentage),
                    averageApplicationsPerJob: totalJobs > 0 ? (totalApplications / totalJobs).toFixed(1) : '0'
                },
                recentActivity: recentApplications.map(app => ({
                    id: app.id,
                    type: 'application',
                    message: `${app.user.firstName || app.user.email} applied for ${app.job.title} at ${app.job.companyName}`,
                    timestamp: app.appliedAt,
                    user: {
                        name: app.user.firstName && app.user.lastName
                            ? `${app.user.firstName} ${app.user.lastName}`
                            : app.user.email,
                        email: app.user.email
                    },
                    job: {
                        title: app.job.title,
                        company: app.job.companyName
                    }
                }))
            };
            res.json(stats);
        }
        catch (error) {
            next(error);
        }
    },
    getJobs: async (req, res, next) => {
        try {
            const filters = admin_1.adminJobFiltersSchema.parse(req.query);
            const { page, limit, search, category, isActive } = filters;
            const skip = (page - 1) * limit;
            const where = {};
            if (category) {
                where.category = { contains: category, mode: 'insensitive' };
            }
            if (search) {
                where.OR = [
                    { title: { contains: search, mode: 'insensitive' } },
                    { description: { contains: search, mode: 'insensitive' } },
                    { companyName: { contains: search, mode: 'insensitive' } },
                ];
            }
            if (isActive !== undefined) {
                where.isActive = isActive;
            }
            const [jobs, total] = await Promise.all([
                db_1.db.job.findMany({
                    where,
                    include: {
                        _count: {
                            select: { applications: true }
                        }
                    },
                    orderBy: { createdAt: 'desc' },
                    skip,
                    take: limit,
                }),
                db_1.db.job.count({ where })
            ]);
            const result = (0, utils_1.createPaginationResult)(jobs, total, page, limit);
            res.json(result);
        }
        catch (error) {
            next(error);
        }
    },
    createJob: async (req, res, next) => {
        try {
            const validatedData = job_1.createJobSchema.parse(req.body);
            const slug = (0, utils_1.generateUniqueSlug)(validatedData.title);
            const job = await db_1.db.job.create({
                data: {
                    ...validatedData,
                    slug,
                    applicationDeadline: validatedData.applicationDeadline
                        ? new Date(validatedData.applicationDeadline)
                        : null,
                },
            });
            res.status(201).json({
                message: 'Job created successfully',
                job,
            });
        }
        catch (error) {
            next(error);
        }
    },
    getJobById: async (req, res, next) => {
        try {
            const { jobId } = req.params;
            const job = await db_1.db.job.findUnique({
                where: { id: jobId },
                include: {
                    _count: {
                        select: { applications: true }
                    }
                }
            });
            if (!job) {
                throw (0, errorHandler_1.createError)('Job not found', 404);
            }
            res.json({ job });
        }
        catch (error) {
            next(error);
        }
    },
    updateJob: async (req, res, next) => {
        try {
            const { jobId } = req.params;
            const validatedData = job_1.updateJobSchema.parse(req.body);
            let updateData = { ...validatedData };
            if (validatedData.title) {
                updateData.slug = (0, utils_1.generateUniqueSlug)(validatedData.title);
            }
            if (validatedData.applicationDeadline) {
                updateData.applicationDeadline = new Date(validatedData.applicationDeadline);
            }
            const job = await db_1.db.job.update({
                where: { id: jobId },
                data: updateData,
            });
            res.json({
                message: 'Job updated successfully',
                job,
            });
        }
        catch (error) {
            next(error);
        }
    },
    deleteJob: async (req, res, next) => {
        try {
            const { jobId } = req.params;
            await db_1.db.job.delete({
                where: { id: jobId },
            });
            res.json({ message: 'Job deleted successfully' });
        }
        catch (error) {
            next(error);
        }
    },
    getUsers: async (req, res, next) => {
        try {
            const filters = admin_1.adminUserFiltersSchema.parse(req.query);
            const { page, limit, search, experienceLevel } = filters;
            const skip = (page - 1) * limit;
            const where = {
                role: 'USER',
            };
            if (search) {
                where.OR = [
                    { email: { contains: search, mode: 'insensitive' } },
                    { firstName: { contains: search, mode: 'insensitive' } },
                    { lastName: { contains: search, mode: 'insensitive' } },
                ];
            }
            if (experienceLevel) {
                where.experienceLevel = experienceLevel;
            }
            const [users, total] = await Promise.all([
                db_1.db.user.findMany({
                    where,
                    select: {
                        id: true,
                        email: true,
                        firstName: true,
                        lastName: true,
                        phone: true,
                        experienceLevel: true,
                        profileCompleted: true,
                        location: true,
                        createdAt: true,
                        _count: {
                            select: { applications: true }
                        }
                    },
                    orderBy: { createdAt: 'desc' },
                    skip,
                    take: limit,
                }),
                db_1.db.user.count({ where })
            ]);
            const result = (0, utils_1.createPaginationResult)(users, total, page, limit);
            res.json(result);
        }
        catch (error) {
            next(error);
        }
    },
    getUserById: async (req, res, next) => {
        try {
            const { userId } = req.params;
            const user = await db_1.db.user.findUnique({
                where: { id: userId },
                select: {
                    id: true,
                    email: true,
                    firstName: true,
                    lastName: true,
                    phone: true,
                    experienceLevel: true,
                    profilePicture: true,
                    resume: true,
                    bio: true,
                    skills: true,
                    location: true,
                    website: true,
                    linkedin: true,
                    github: true,
                    profileCompleted: true,
                    createdAt: true,
                    updatedAt: true,
                    _count: {
                        select: { applications: true }
                    }
                }
            });
            if (!user) {
                throw (0, errorHandler_1.createError)('User not found', 404);
            }
            const recentApplications = await db_1.db.application.findMany({
                where: { userId },
                include: {
                    job: {
                        select: {
                            title: true,
                            companyName: true,
                            location: true,
                        }
                    }
                },
                orderBy: { appliedAt: 'desc' },
                take: 5,
            });
            res.json({ user, recentApplications });
        }
        catch (error) {
            next(error);
        }
    },
    deleteUser: async (req, res, next) => {
        try {
            const { userId } = req.params;
            const user = await db_1.db.user.findUnique({
                where: { id: userId },
                select: { role: true, email: true }
            });
            if (!user) {
                throw (0, errorHandler_1.createError)('User not found', 404);
            }
            if (user.role === 'ADMIN') {
                throw (0, errorHandler_1.createError)('Cannot delete admin user', 403);
            }
            await db_1.db.user.delete({
                where: { id: userId }
            });
            res.json({
                message: 'User deleted successfully',
                deletedUser: { email: user.email }
            });
        }
        catch (error) {
            next(error);
        }
    },
    getApplications: async (req, res, next) => {
        try {
            const filters = admin_1.adminApplicationFiltersSchema.parse(req.query);
            const { page, limit, jobId, status } = filters;
            const skip = (page - 1) * limit;
            const where = {};
            if (jobId) {
                where.jobId = jobId;
            }
            if (status) {
                where.status = status;
            }
            const [applications, total] = await Promise.all([
                db_1.db.application.findMany({
                    where,
                    include: {
                        user: {
                            select: {
                                id: true,
                                email: true,
                                firstName: true,
                                lastName: true,
                                phone: true,
                                experienceLevel: true,
                                resume: true,
                                profilePicture: true,
                            }
                        },
                        job: {
                            select: {
                                id: true,
                                title: true,
                                companyName: true,
                                location: true,
                            }
                        }
                    },
                    orderBy: { appliedAt: 'desc' },
                    skip,
                    take: limit,
                }),
                db_1.db.application.count({ where })
            ]);
            const result = (0, utils_1.createPaginationResult)(applications, total, page, limit);
            res.json(result);
        }
        catch (error) {
            next(error);
        }
    },
    updateApplicationStatus: async (req, res, next) => {
        try {
            const { applicationId } = req.params;
            const { status } = admin_1.updateApplicationStatusSchema.parse(req.body);
            const application = await db_1.db.application.update({
                where: { id: applicationId },
                data: { status },
                include: {
                    user: {
                        select: {
                            email: true,
                            firstName: true,
                            lastName: true,
                        }
                    },
                    job: {
                        select: {
                            title: true,
                            companyName: true,
                        }
                    }
                }
            });
            res.json({
                message: 'Application status updated successfully',
                application,
            });
        }
        catch (error) {
            next(error);
        }
    },
    getBlogs: async (req, res, next) => {
        try {
            const filters = admin_1.adminBlogFiltersSchema.parse(req.query);
            const { page, limit, search, isPublished } = filters;
            const skip = (page - 1) * limit;
            const where = {};
            if (search) {
                where.OR = [
                    { title: { contains: search, mode: 'insensitive' } },
                    { excerpt: { contains: search, mode: 'insensitive' } },
                    { content: { contains: search, mode: 'insensitive' } },
                ];
            }
            if (isPublished !== undefined) {
                where.isPublished = isPublished;
            }
            const [blogs, total] = await Promise.all([
                db_1.db.blog.findMany({
                    where,
                    orderBy: { createdAt: 'desc' },
                    skip,
                    take: limit,
                }),
                db_1.db.blog.count({ where })
            ]);
            const result = (0, utils_1.createPaginationResult)(blogs, total, page, limit);
            res.json(result);
        }
        catch (error) {
            next(error);
        }
    },
    createBlog: async (req, res, next) => {
        try {
            const validatedData = blog_1.createBlogSchema.parse(req.body);
            const slug = (0, utils_1.generateUniqueSlug)(validatedData.title);
            const structuredData = {
                "@context": "https://schema.org",
                "@type": "BlogPosting",
                "headline": validatedData.title,
                "description": validatedData.excerpt || validatedData.content.substring(0, 160),
                "author": {
                    "@type": "Organization",
                    "name": "Job Portal"
                },
                "datePublished": new Date().toISOString(),
                "dateModified": new Date().toISOString(),
            };
            const blog = await db_1.db.blog.create({
                data: {
                    ...validatedData,
                    slug,
                    structuredData,
                    publishedAt: validatedData.isPublished ? new Date() : null,
                },
            });
            res.status(201).json({
                message: 'Blog created successfully',
                blog,
            });
        }
        catch (error) {
            next(error);
        }
    },
    getBlogById: async (req, res, next) => {
        try {
            const { blogId } = req.params;
            const blog = await db_1.db.blog.findUnique({
                where: { id: blogId },
            });
            if (!blog) {
                throw (0, errorHandler_1.createError)('Blog not found', 404);
            }
            res.json({ blog });
        }
        catch (error) {
            next(error);
        }
    },
    updateBlog: async (req, res, next) => {
        try {
            const { blogId } = req.params;
            const validatedData = blog_1.updateBlogSchema.parse(req.body);
            const currentBlog = await db_1.db.blog.findUnique({
                where: { id: blogId }
            });
            if (!currentBlog) {
                throw (0, errorHandler_1.createError)('Blog not found', 404);
            }
            let updateData = { ...validatedData };
            if (validatedData.title && validatedData.title !== currentBlog.title) {
                updateData.slug = (0, utils_1.generateUniqueSlug)(validatedData.title);
            }
            if (validatedData.title || validatedData.excerpt) {
                updateData.structuredData = {
                    "@context": "https://schema.org",
                    "@type": "BlogPosting",
                    "headline": validatedData.title || currentBlog.title,
                    "description": validatedData.excerpt || currentBlog.excerpt || (validatedData.content || currentBlog.content).substring(0, 160),
                    "author": {
                        "@type": "Organization",
                        "name": "Job Portal"
                    },
                    "datePublished": currentBlog.publishedAt?.toISOString() || currentBlog.createdAt.toISOString(),
                    "dateModified": new Date().toISOString(),
                };
            }
            if (validatedData.isPublished !== undefined) {
                if (validatedData.isPublished && !currentBlog.isPublished) {
                    updateData.publishedAt = new Date();
                }
                else if (!validatedData.isPublished && currentBlog.isPublished) {
                    updateData.publishedAt = null;
                }
            }
            const blog = await db_1.db.blog.update({
                where: { id: blogId },
                data: updateData,
            });
            res.json({
                message: 'Blog updated successfully',
                blog,
            });
        }
        catch (error) {
            next(error);
        }
    },
    deleteBlog: async (req, res, next) => {
        try {
            const { blogId } = req.params;
            await db_1.db.blog.delete({
                where: { id: blogId },
            });
            res.json({ message: 'Blog deleted successfully' });
        }
        catch (error) {
            next(error);
        }
    }
};
//# sourceMappingURL=adminController.js.map