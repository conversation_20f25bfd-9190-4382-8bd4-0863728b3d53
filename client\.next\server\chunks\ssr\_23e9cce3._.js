module.exports = {

"[project]/.next-internal/server/app/jobs/[slug]/page/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[project]/src/app/favicon.ico.mjs { IMAGE => \"[project]/src/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/favicon.ico.mjs { IMAGE => \"[project]/src/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript)"));
}}),
"[project]/src/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/layout.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/src/app/not-found.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/not-found.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/src/components/jobs/job-details-client.tsx [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const e = new Error(`Could not parse module '[project]/src/components/jobs/job-details-client.tsx'

Unexpected token `main`. Expected jsx identifier`);
e.code = 'MODULE_UNPARSEABLE';
throw e;}}),
"[project]/src/lib/api.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "api": (()=>api),
    "formatDate": (()=>formatDate),
    "formatSalary": (()=>formatSalary),
    "getExperienceLabel": (()=>getExperienceLabel),
    "getJobTypeLabel": (()=>getJobTypeLabel),
    "getWorkTypeLabel": (()=>getWorkTypeLabel)
});
const API_BASE_URL = ("TURBOPACK compile-time value", "http://localhost:5000/api") || 'http://localhost:5000/api';
// API Client Class
class ApiClient {
    baseURL;
    token = null;
    constructor(baseURL){
        this.baseURL = baseURL;
        // Initialize token from localStorage if available
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
    }
    setToken(token) {
        this.token = token;
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
    }
    clearToken() {
        this.token = null;
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
    }
    async request(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        const headers = {
            'Content-Type': 'application/json',
            ...options.headers
        };
        if (this.token) {
            headers.Authorization = `Bearer ${this.token}`;
        }
        try {
            const response = await fetch(url, {
                ...options,
                headers
            });
            if (!response.ok) {
                const errorData = await response.json().catch(()=>({
                        message: response.status === 404 ? 'Resource not found' : 'Network error',
                        status: response.status
                    }));
                const error = new Error(errorData.message || `HTTP ${response.status}`);
                error.status = response.status;
                error.code = errorData.code;
                throw error;
            }
            return response.json();
        } catch (error) {
            // Handle network errors
            if (error instanceof TypeError || !navigator.onLine) {
                const networkError = new Error('Network error. Please check your internet connection.');
                networkError.status = 0;
                throw networkError;
            }
            throw error;
        }
    }
    // Auth endpoints
    async login(email, password) {
        return this.request('/auth/login', {
            method: 'POST',
            body: JSON.stringify({
                email,
                password
            })
        });
    }
    async register(data) {
        return this.request('/auth/register', {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }
    async getProfile() {
        const response = await this.request('/auth/me');
        return response.user;
    }
    async updateProfile(data) {
        return this.request('/user/profile', {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }
    // Jobs endpoints
    async getJobs(filters = {}, page = 1, limit = 12) {
        const params = new URLSearchParams({
            page: page.toString(),
            limit: limit.toString(),
            ...Object.fromEntries(Object.entries(filters).filter(([, value])=>value !== undefined && value !== ''))
        });
        return this.request(`/jobs?${params}`);
    }
    async getJob(slug) {
        return this.request(`/jobs/${slug}`);
    }
    async getFeaturedJobs() {
        try {
            const result = await this.request('/jobs/featured');
            return Array.isArray(result.jobs) ? result.jobs : [];
        } catch (error) {
            console.warn('Failed to fetch featured jobs:', error);
            return [];
        }
    }
    async applyToJob(jobId, coverLetter) {
        return this.request(`/jobs/${jobId}/apply`, {
            method: 'POST',
            body: JSON.stringify({
                message: coverLetter
            })
        });
    }
    async getMyApplications() {
        return this.request('/user/applications');
    }
    async getJobCategories() {
        return this.request('/jobs/categories');
    }
    // Blog endpoints
    async getBlogs(page = 1, limit = 10) {
        const params = new URLSearchParams({
            page: page.toString(),
            limit: limit.toString()
        });
        return this.request(`/blogs?${params}`);
    }
    async getBlog(slug) {
        return this.request(`/blogs/${slug}`);
    }
    // File upload
    async uploadFile(file, type) {
        const formData = new FormData();
        formData.append('file', file);
        const headers = {};
        if (this.token) {
            headers.Authorization = `Bearer ${this.token}`;
        }
        // Use specific endpoints based on type
        const endpoint = type === 'resume' ? '/upload/resume' : '/upload/profile-picture';
        const response = await fetch(`${this.baseURL}${endpoint}`, {
            method: 'POST',
            headers,
            body: formData
        });
        if (!response.ok) {
            const error = await response.json().catch(()=>({
                    message: 'Upload failed'
                }));
            throw new Error(error.message || `HTTP ${response.status}`);
        }
        const result = await response.json();
        // Normalize response format - backend returns different field names
        if (type === 'resume') {
            return {
                url: result.resume
            };
        } else {
            return {
                url: result.profilePicture
            };
        }
    }
    // Contact form
    async submitContactForm(data) {
        return this.request('/contact', {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }
    // Email verification
    async sendVerificationEmail(email) {
        return this.request('/auth/send-verification', {
            method: 'POST',
            body: JSON.stringify({
                email
            })
        });
    }
    async resendVerificationEmail(email) {
        return this.sendVerificationEmail(email);
    }
    async verifyEmail(token) {
        return this.request('/auth/verify-email', {
            method: 'POST',
            body: JSON.stringify({
                token
            })
        });
    }
    // Password reset
    async forgotPassword(email) {
        return this.request('/auth/forgot-password', {
            method: 'POST',
            body: JSON.stringify({
                email
            })
        });
    }
    async resetPassword(token, password) {
        return this.request('/auth/reset-password', {
            method: 'POST',
            body: JSON.stringify({
                token,
                password
            })
        });
    }
}
const api = new ApiClient(API_BASE_URL);
const formatSalary = (min, max, negotiable)=>{
    if (negotiable) return 'Negotiable';
    if (!min && !max) return 'Not specified';
    if (min && max) return `$${min.toLocaleString()} - $${max.toLocaleString()}`;
    if (min) return `$${min.toLocaleString()}+`;
    if (max) return `Up to $${max.toLocaleString()}`;
    return 'Not specified';
};
const formatDate = (dateString)=>{
    return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
};
const getJobTypeLabel = (type)=>{
    const labels = {
        'FULL_TIME': 'Full Time',
        'PART_TIME': 'Part Time',
        'CONTRACT': 'Contract',
        'INTERNSHIP': 'Internship',
        // Legacy support
        'full-time': 'Full Time',
        'part-time': 'Part Time',
        'contract': 'Contract',
        'freelance': 'Freelance',
        'internship': 'Internship'
    };
    return labels[type] || type;
};
const getExperienceLabel = (level)=>{
    const labels = {
        'STUDENT': 'Student / Currently Studying',
        'FRESHER': 'Fresher',
        'INTERNSHIP_ONLY': 'Internship Experience Only',
        'ZERO_TO_ONE_YEAR': '0–1 Year',
        'ONE_TO_THREE_YEARS': '1–3 Years',
        'THREE_TO_FIVE_YEARS': '3–5 Years',
        'FIVE_PLUS_YEARS': '5+ Years',
        // Legacy support
        'THREE_PLUS_YEARS': '3+ Years',
        'entry': 'Entry Level',
        'mid': 'Mid Level',
        'senior': 'Senior Level',
        'lead': 'Lead',
        'executive': 'Executive'
    };
    return labels[level] || level;
};
const getWorkTypeLabel = (type)=>{
    const labels = {
        'ONSITE': 'On-site',
        'REMOTE': 'Remote',
        'HYBRID': 'Hybrid',
        // Legacy support
        'onsite': 'On-site',
        'remote': 'Remote',
        'hybrid': 'Hybrid'
    };
    return labels[type] || type;
};
}}),
"[project]/src/app/jobs/[slug]/page.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>JobPage),
    "generateMetadata": (()=>generateMetadata)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$api$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/api/navigation.react-server.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/components/navigation.react-server.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$jobs$2f$job$2d$details$2d$client$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/jobs/job-details-client.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api.ts [app-rsc] (ecmascript)");
;
;
;
;
async function generateMetadata({ params }) {
    const resolvedParams = await params;
    console.log(resolvedParams.slug);
    try {
        const jobData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["api"].getJob(resolvedParams.slug);
        const job = jobData?.job;
        console.log(job);
        return {
            title: `${job.title} at ${job.companyName} | JobPortal`,
            description: job.description.replace(/<[^>]*>/g, '').substring(0, 160),
            keywords: [
                job.title,
                job.companyName,
                job.category,
                job.location,
                'job',
                'career'
            ],
            openGraph: {
                title: `${job.title} at ${job.companyName}`,
                description: job.description.replace(/<[^>]*>/g, '').substring(0, 160),
                type: 'article',
                locale: 'en_US',
                images: job.companyLogo ? [
                    {
                        url: job.companyLogo
                    }
                ] : []
            },
            twitter: {
                card: 'summary_large_image',
                title: `${job.title} at ${job.companyName}`,
                description: job.description.replace(/<[^>]*>/g, '').substring(0, 160),
                images: job.companyLogo ? [
                    job.companyLogo
                ] : []
            },
            alternates: {
                canonical: `/jobs/${job.slug}`
            }
        };
    } catch  {
        return {
            title: 'Job Not Found | JobPortal',
            description: 'The job you are looking for could not be found.'
        };
    }
}
async function JobPage({ params }) {
    try {
        const resolvedParams = await params;
        const jobData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["api"].getJob(resolvedParams.slug);
        console.log(jobData?.hasApplied);
        const job = jobData?.job;
        const structuredData = {
            '@context': 'https://schema.org',
            '@type': 'JobPosting',
            title: job.title,
            description: job.description.replace(/<[^>]*>/g, ''),
            identifier: {
                '@type': 'PropertyValue',
                name: job.companyName,
                value: job.id
            },
            datePosted: job.createdAt,
            validThrough: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
            employmentType: job.jobType.toUpperCase().replace('-', '_'),
            hiringOrganization: {
                '@type': 'Organization',
                name: job.companyName,
                logo: job.companyLogo
            },
            jobLocation: {
                '@type': 'Place',
                address: {
                    '@type': 'PostalAddress',
                    addressLocality: job.location
                }
            },
            baseSalary: job.salaryMin && job.salaryMax ? {
                '@type': 'MonetaryAmount',
                currency: 'USD',
                value: {
                    '@type': 'QuantitativeValue',
                    minValue: job.salaryMin,
                    maxValue: job.salaryMax,
                    unitText: 'YEAR'
                }
            } : undefined,
            workHours: job.jobType === 'FULL_TIME' ? '40 hours per week' : undefined
        };
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Fragment"], {
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("script", {
                    type: "application/ld+json",
                    dangerouslySetInnerHTML: {
                        __html: JSON.stringify(structuredData)
                    }
                }, void 0, false, {
                    fileName: "[project]/src/app/jobs/[slug]/page.tsx",
                    lineNumber: 94,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$jobs$2f$job$2d$details$2d$client$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["JobDetailsClient"], {
                    job: job,
                    hasApplied: jobData?.hasApplied
                }, void 0, false, {
                    fileName: "[project]/src/app/jobs/[slug]/page.tsx",
                    lineNumber: 98,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true);
    } catch  {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["notFound"])();
    }
}
}}),
"[project]/src/app/jobs/[slug]/page.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/jobs/[slug]/page.tsx [app-rsc] (ecmascript)"));
}}),

};

//# sourceMappingURL=_23e9cce3._.js.map