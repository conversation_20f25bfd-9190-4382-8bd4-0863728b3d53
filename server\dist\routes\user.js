"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const db_1 = require("../lib/db");
const auth_1 = require("../middleware/auth");
const user_1 = require("../schemas/user");
const errorHandler_1 = require("../middleware/errorHandler");
const router = express_1.default.Router();
router.put('/profile', auth_1.authenticate, async (req, res, next) => {
    try {
        const validatedData = user_1.updateProfileSchema.parse(req.body);
        const currentUser = await db_1.db.user.findUnique({
            where: { id: req.user.id },
            select: {
                firstName: true,
                lastName: true,
                bio: true,
                skills: true,
                location: true,
                resume: true,
            }
        });
        const mergedData = {
            firstName: validatedData.firstName || currentUser?.firstName,
            lastName: validatedData.lastName || currentUser?.lastName,
            bio: validatedData.bio || currentUser?.bio,
            skills: validatedData.skills || currentUser?.skills,
            location: validatedData.location || currentUser?.location,
            resume: currentUser?.resume,
        };
        const isProfileComplete = !!(mergedData.firstName &&
            mergedData.lastName &&
            mergedData.bio &&
            mergedData.skills && mergedData.skills.length > 0 &&
            mergedData.location &&
            mergedData.resume);
        const updatedUser = await db_1.db.user.update({
            where: { id: req.user.id },
            data: {
                ...validatedData,
                profileCompleted: isProfileComplete,
                updatedAt: new Date(),
            },
            select: {
                id: true,
                email: true,
                phone: true,
                experienceLevel: true,
                role: true,
                firstName: true,
                lastName: true,
                profilePicture: true,
                resume: true,
                bio: true,
                skills: true,
                experience: true,
                education: true,
                location: true,
                website: true,
                linkedin: true,
                github: true,
                profileCompleted: true,
                createdAt: true,
                updatedAt: true,
            }
        });
        res.json({
            message: 'Profile updated successfully',
            user: updatedUser,
        });
    }
    catch (error) {
        next(error);
    }
});
router.get('/applications', auth_1.authenticate, async (req, res, next) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = Math.min(parseInt(req.query.limit) || 10, 100);
        const skip = (page - 1) * limit;
        const [applications, total] = await Promise.all([
            db_1.db.application.findMany({
                where: { userId: req.user.id },
                include: {
                    job: {
                        select: {
                            id: true,
                            title: true,
                            slug: true,
                            companyName: true,
                            location: true,
                            jobType: true,
                            salaryMin: true,
                            salaryMax: true,
                            currency: true,
                            isActive: true,
                        }
                    }
                },
                orderBy: { appliedAt: 'desc' },
                skip,
                take: limit,
            }),
            db_1.db.application.count({
                where: { userId: req.user.id }
            })
        ]);
        const totalPages = Math.ceil(total / limit);
        res.json({
            applications,
            pagination: {
                page,
                limit,
                total,
                totalPages,
                hasNext: page < totalPages,
                hasPrev: page > 1,
            }
        });
    }
    catch (error) {
        next(error);
    }
});
router.get('/applications/:applicationId', auth_1.authenticate, async (req, res, next) => {
    try {
        const { applicationId } = req.params;
        const application = await db_1.db.application.findFirst({
            where: {
                id: applicationId,
                userId: req.user.id,
            },
            include: {
                job: {
                    select: {
                        id: true,
                        title: true,
                        slug: true,
                        description: true,
                        requirements: true,
                        responsibilities: true,
                        companyName: true,
                        companyLogo: true,
                        companyWebsite: true,
                        location: true,
                        jobType: true,
                        experienceLevel: true,
                        salaryMin: true,
                        salaryMax: true,
                        currency: true,
                        isActive: true,
                        createdAt: true,
                    }
                }
            }
        });
        if (!application) {
            throw (0, errorHandler_1.createError)('Application not found', 404);
        }
        res.json({ application });
    }
    catch (error) {
        next(error);
    }
});
exports.default = router;
//# sourceMappingURL=user.js.map