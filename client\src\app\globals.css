@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.5rem;

  --background: oklch(0.99 0.001 240);        /* clean white */
  --foreground: oklch(0.15 0.01 240);         /* professional dark gray */

  --card: oklch(0.98 0.001 240);              /* subtle card background */
  --card-foreground: oklch(0.15 0.01 240);

  --popover: oklch(0.98 0.001 240);
  --popover-foreground: oklch(0.15 0.01 240);

  --primary: oklch(0.35 0.15 220);            /* professional navy blue */
  --primary-foreground: oklch(0.98 0.001 220);

  --secondary: oklch(0.92 0.02 240);          /* light gray */
  --secondary-foreground: oklch(0.25 0.01 240);

  --muted: oklch(0.95 0.002 240);             /* subtle gray */
  --muted-foreground: oklch(0.55 0.01 240);

  --accent: oklch(0.45 0.12 200);             /* professional teal */
  --accent-foreground: oklch(0.98 0.001 200);

  --destructive: oklch(0.55 0.18 15);         /* professional red */

  --border: oklch(0.90 0.002 240);
  --input: oklch(0.96 0.002 240);
  --ring: oklch(0.35 0.15 220);

  --chart-1: oklch(0.35 0.15 220);   /* navy blue */
  --chart-2: oklch(0.45 0.12 200);   /* teal */
  --chart-3: oklch(0.40 0.10 180);   /* blue */
  --chart-4: oklch(0.50 0.08 160);   /* light blue */
  --chart-5: oklch(0.60 0.06 140);   /* mint */
}


.dark {
  --background: oklch(0.10 0.01 240);        /* professional dark */
  --foreground: oklch(0.92 0.002 240);       /* clean white text */

  --card: oklch(0.14 0.01 240);              /* dark card background */
  --card-foreground: oklch(0.92 0.002 240);

  --popover: oklch(0.14 0.01 240);
  --popover-foreground: oklch(0.92 0.002 240);

  --primary: oklch(0.55 0.15 220);           /* professional blue */
  --primary-foreground: oklch(0.10 0.01 220);

  --secondary: oklch(0.20 0.02 240);         /* dark gray */
  --secondary-foreground: oklch(0.85 0.002 240);

  --muted: oklch(0.18 0.01 240);             /* subtle dark gray */
  --muted-foreground: oklch(0.65 0.01 240);

  --accent: oklch(0.60 0.12 200);            /* professional teal */
  --accent-foreground: oklch(0.10 0.01 200);

  --destructive: oklch(0.65 0.18 15);        /* professional red */

  --border: oklch(0.22 0.01 240);
  --input: oklch(0.18 0.01 240);
  --ring: oklch(0.55 0.15 220);

  --chart-1: oklch(0.55 0.15 220);   /* professional blue */
  --chart-2: oklch(0.60 0.12 200);   /* professional teal */
  --chart-3: oklch(0.50 0.10 180);   /* blue */
  --chart-4: oklch(0.65 0.08 160);   /* light blue */
  --chart-5: oklch(0.70 0.06 140);   /* mint */
}


@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground font-medium;
  }

  /* Modern scrollbar */
  ::-webkit-scrollbar {
    width: 6px;
  }
  ::-webkit-scrollbar-track {
    background: transparent;
  }
  ::-webkit-scrollbar-thumb {
    background: oklch(0.45 0.25 280);
    border-radius: 3px;
  }
  ::-webkit-scrollbar-thumb:hover {
    background: oklch(0.35 0.25 280);
  }
}

@layer components {
  /* Professional glass effect */
  .glass {
    @apply backdrop-blur-sm bg-white/90 border border-border/50;
  }

  .glass-dark {
    @apply backdrop-blur-sm bg-black/80 border border-border/30;
  }

  /* Subtle shadow effects */
  .glow-primary {
    box-shadow: 0 4px 20px oklch(0.35 0.15 220 / 0.15);
  }

  .glow-accent {
    box-shadow: 0 4px 20px oklch(0.45 0.12 200 / 0.15);
  }

  /* Professional gradient backgrounds */
  .gradient-primary {
    background: linear-gradient(135deg, oklch(0.35 0.15 220) 0%, oklch(0.40 0.12 200) 100%);
  }

  .gradient-accent {
    background: linear-gradient(135deg, oklch(0.45 0.12 200) 0%, oklch(0.40 0.10 180) 100%);
  }

  /* Floating animation */
  .float {
    animation: float 6s ease-in-out infinite;
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }

  /* Pulse glow animation */
  .pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite alternate;
  }

  @keyframes pulse-glow {
    from { box-shadow: 0 0 20px oklch(0.45 0.25 280 / 0.2); }
    to { box-shadow: 0 0 30px oklch(0.45 0.25 280 / 0.4); }
  }
}
