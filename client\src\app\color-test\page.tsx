import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

export default function ColorTestPage() {
  return (
    <div className="container mx-auto px-4 py-8 space-y-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="mb-8">Color Theme Implementation Test</h1>
        
        <div className="space-y-12">
          {/* Color Palette Overview */}
          <section className="space-y-6">
            <h2 className="text-2xl font-semibold mb-4">Color Palette Overview</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {/* Primary - Royal Blue */}
              <Card className="text-center">
                <CardContent className="p-4">
                  <div className="w-full h-20 bg-primary rounded-lg mb-3"></div>
                  <h3 className="font-semibold">Primary</h3>
                  <p className="text-sm text-muted-foreground">Royal Blue #2563EB</p>
                  <p className="text-xs">Trust, professionalism</p>
                </CardContent>
              </Card>

              {/* Background - Off White */}
              <Card className="text-center">
                <CardContent className="p-4">
                  <div className="w-full h-20 bg-background border rounded-lg mb-3"></div>
                  <h3 className="font-semibold">Background</h3>
                  <p className="text-sm text-muted-foreground">Off White #F9FAFB</p>
                  <p className="text-xs">Clean, spacious</p>
                </CardContent>
              </Card>

              {/* Success - Emerald Green */}
              <Card className="text-center">
                <CardContent className="p-4">
                  <div className="w-full h-20 bg-success rounded-lg mb-3"></div>
                  <h3 className="font-semibold">Success</h3>
                  <p className="text-sm text-muted-foreground">Emerald #10B981</p>
                  <p className="text-xs">Positive signals</p>
                </CardContent>
              </Card>

              {/* Warning - Sun Yellow */}
              <Card className="text-center">
                <CardContent className="p-4">
                  <div className="w-full h-20 bg-warning rounded-lg mb-3"></div>
                  <h3 className="font-semibold">Warning</h3>
                  <p className="text-sm text-muted-foreground">Sun Yellow #FACC15</p>
                  <p className="text-xs">Attention-grabbing</p>
                </CardContent>
              </Card>

              {/* Destructive - Soft Red */}
              <Card className="text-center">
                <CardContent className="p-4">
                  <div className="w-full h-20 bg-destructive rounded-lg mb-3"></div>
                  <h3 className="font-semibold">Error</h3>
                  <p className="text-sm text-muted-foreground">Soft Red #EF4444</p>
                  <p className="text-xs">Alert but gentle</p>
                </CardContent>
              </Card>

              {/* Accent - Soft Indigo */}
              <Card className="text-center">
                <CardContent className="p-4">
                  <div className="w-full h-20 bg-accent rounded-lg mb-3"></div>
                  <h3 className="font-semibold">Accent</h3>
                  <p className="text-sm text-muted-foreground">Soft Indigo #6366F1</p>
                  <p className="text-xs">Confidence, innovation</p>
                </CardContent>
              </Card>

              {/* CTA - Vibrant Orange */}
              <Card className="text-center">
                <CardContent className="p-4">
                  <div className="w-full h-20 bg-cta rounded-lg mb-3"></div>
                  <h3 className="font-semibold">CTA</h3>
                  <p className="text-sm text-muted-foreground">Vibrant Orange #F97316</p>
                  <p className="text-xs">Energy, urgency</p>
                </CardContent>
              </Card>

              {/* Foreground - Slate Black */}
              <Card className="text-center">
                <CardContent className="p-4">
                  <div className="w-full h-20 bg-foreground rounded-lg mb-3"></div>
                  <h3 className="font-semibold">Text</h3>
                  <p className="text-sm text-muted-foreground">Slate Black #111827</p>
                  <p className="text-xs">Clarity, focus</p>
                </CardContent>
              </Card>
            </div>
          </section>

          {/* Button Variations */}
          <section className="space-y-6">
            <h2 className="text-2xl font-semibold mb-4">Button Variations</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="space-y-3">
                <h3 className="font-medium">Primary Actions</h3>
                <Button className="w-full">Primary Button</Button>
                <Button variant="outline" className="w-full">Outline Button</Button>
              </div>
              
              <div className="space-y-3">
                <h3 className="font-medium">Secondary Actions</h3>
                <Button variant="secondary" className="w-full">Secondary Button</Button>
                <Button variant="ghost" className="w-full">Ghost Button</Button>
              </div>
              
              <div className="space-y-3">
                <h3 className="font-medium">Status Actions</h3>
                <Button variant="destructive" className="w-full">Delete Action</Button>
                <Button className="w-full bg-success hover:bg-success/90">Success Action</Button>
              </div>
              
              <div className="space-y-3">
                <h3 className="font-medium">CTA Actions</h3>
                <Button className="w-full bg-cta hover:bg-cta/90 text-cta-foreground">Call to Action</Button>
                <Button className="w-full bg-warning hover:bg-warning/90 text-warning-foreground">Warning Action</Button>
              </div>
            </div>
          </section>

          {/* Badge Variations */}
          <section className="space-y-6">
            <h2 className="text-2xl font-semibold mb-4">Badge Variations</h2>
            <div className="flex flex-wrap gap-3">
              <Badge>Default Badge</Badge>
              <Badge variant="secondary">Secondary Badge</Badge>
              <Badge variant="outline">Outline Badge</Badge>
              <Badge variant="destructive">Error Badge</Badge>
              <Badge className="bg-success text-success-foreground">Success Badge</Badge>
              <Badge className="bg-warning text-warning-foreground">Warning Badge</Badge>
              <Badge className="bg-cta text-cta-foreground">CTA Badge</Badge>
              <Badge className="bg-accent text-accent-foreground">Accent Badge</Badge>
            </div>
          </section>

          {/* Card Examples */}
          <section className="space-y-6">
            <h2 className="text-2xl font-semibold mb-4">Card Examples</h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              <Card className="glow-primary">
                <CardHeader>
                  <CardTitle>Primary Card</CardTitle>
                  <CardDescription>This card uses primary color glow effect</CardDescription>
                </CardHeader>
                <CardContent>
                  <p>Content with primary theme styling.</p>
                </CardContent>
              </Card>

              <Card className="glow-success">
                <CardHeader>
                  <CardTitle>Success Card</CardTitle>
                  <CardDescription>This card uses success color glow effect</CardDescription>
                </CardHeader>
                <CardContent>
                  <p>Content with success theme styling.</p>
                </CardContent>
              </Card>

              <Card className="glow-cta">
                <CardHeader>
                  <CardTitle>CTA Card</CardTitle>
                  <CardDescription>This card uses CTA color glow effect</CardDescription>
                </CardHeader>
                <CardContent>
                  <p>Content with CTA theme styling.</p>
                </CardContent>
              </Card>
            </div>
          </section>

          {/* Gradient Examples */}
          <section className="space-y-6">
            <h2 className="text-2xl font-semibold mb-4">Gradient Examples</h2>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="gradient-primary p-6 rounded-lg text-primary-foreground">
                <h3 className="text-xl font-semibold mb-2">Primary Gradient</h3>
                <p>This section uses the primary gradient background.</p>
              </div>
              
              <div className="gradient-cta p-6 rounded-lg text-cta-foreground">
                <h3 className="text-xl font-semibold mb-2">CTA Gradient</h3>
                <p>This section uses the CTA gradient background.</p>
              </div>
            </div>
          </section>

          {/* Text Color Examples */}
          <section className="space-y-6">
            <h2 className="text-2xl font-semibold mb-4">Text Color Examples</h2>
            <div className="space-y-4">
              <p className="text-foreground">Default foreground text (Slate Black)</p>
              <p className="text-muted-foreground">Muted foreground text</p>
              <p className="text-primary">Primary colored text (Royal Blue)</p>
              <p className="text-accent">Accent colored text (Soft Indigo)</p>
              <p className="text-success">Success colored text (Emerald Green)</p>
              <p className="text-warning">Warning colored text (Sun Yellow)</p>
              <p className="text-destructive">Destructive colored text (Soft Red)</p>
              <p className="text-cta">CTA colored text (Vibrant Orange)</p>
            </div>
          </section>

          {/* Interactive Example */}
          <section className="space-y-6">
            <h2 className="text-2xl font-semibold mb-4">Interactive Job Portal Example</h2>
            <Card className="p-6">
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h1>Find Your Dream Job</h1>
                  <Badge className="bg-success text-success-foreground">2,847 Active Jobs</Badge>
                </div>
                
                <p>
                  Connect with top employers and discover opportunities that match your skills and aspirations.
                </p>
                
                <div className="flex flex-wrap gap-3">
                  <Button>Search Jobs</Button>
                  <Button variant="outline">Browse Companies</Button>
                  <Button className="bg-cta hover:bg-cta/90 text-cta-foreground">Post Your Resume</Button>
                </div>
                
                <div className="grid md:grid-cols-3 gap-4 mt-6">
                  <div className="text-center p-4 border rounded-lg">
                    <div className="text-2xl font-bold text-primary">1,200+</div>
                    <div className="text-sm text-muted-foreground">Active Jobs</div>
                  </div>
                  <div className="text-center p-4 border rounded-lg">
                    <div className="text-2xl font-bold text-success">500+</div>
                    <div className="text-sm text-muted-foreground">Companies</div>
                  </div>
                  <div className="text-center p-4 border rounded-lg">
                    <div className="text-2xl font-bold text-cta">10,000+</div>
                    <div className="text-sm text-muted-foreground">Success Stories</div>
                  </div>
                </div>
              </div>
            </Card>
          </section>
        </div>
      </div>
    </div>
  );
}
