'use client'

import { useQuery } from '@tanstack/react-query'
import { api } from '@/lib/api'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { CheckCircle, Loader2, RefreshCw } from 'lucide-react'
import Link from 'next/link'

export default function TestCategoriesPage() {
  const { data: categoriesData, isLoading, error, refetch } = useQuery({
    queryKey: ['job-categories'],
    queryFn: () => api.getJobCategories(),
  })

  const { data: jobsData } = useQuery({
    queryKey: ['jobs-sample'],
    queryFn: () => api.getJobs({}, 1, 5),
  })

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-muted/10 to-secondary/5 p-8">
      <div className="max-w-6xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <div className="w-16 h-16 rounded-3xl bg-gradient-to-br from-primary to-accent flex items-center justify-center mx-auto">
            <CheckCircle className="h-8 w-8 text-white" />
          </div>
          <h1 className="text-4xl font-black text-foreground">
            Categories System Test 🎯
          </h1>
          <p className="text-muted-foreground">
            Testing the job categories implementation
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-8">
          {/* Categories Test */}
          <Card className="glass border-0 rounded-3xl">
            <CardHeader className="bg-gradient-to-r from-primary/5 to-accent/5 border-b border-border/50">
              <CardTitle className="flex items-center justify-between">
                <span>📂 Categories API Test</span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => refetch()}
                  disabled={isLoading}
                  className="rounded-2xl"
                >
                  <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              {isLoading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  <span className="ml-3 text-muted-foreground">Loading categories...</span>
                </div>
              ) : error ? (
                <div className="text-center py-8">
                  <div className="text-destructive font-semibold mb-2">❌ Error loading categories</div>
                  <p className="text-muted-foreground text-sm">{error.message}</p>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="font-semibold text-foreground">Total Categories:</span>
                    <Badge variant="secondary" className="rounded-full">
                      {categoriesData?.total || 0}
                    </Badge>
                  </div>
                  
                  <div className="space-y-2">
                    <h4 className="font-semibold text-foreground">Available Categories:</h4>
                    <div className="grid gap-2 max-h-64 overflow-y-auto">
                      {categoriesData?.categories.map((category, index) => (
                        <div
                          key={category}
                          className="flex items-center justify-between p-3 bg-muted/30 rounded-2xl"
                        >
                          <span className="text-sm font-medium">{category}</span>
                          <Badge variant="outline" className="text-xs">
                            #{index + 1}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="pt-4 border-t border-border/50">
                    <div className="text-center">
                      <div className="text-green-600 font-semibold mb-2">✅ Categories API Working!</div>
                      <p className="text-muted-foreground text-sm">
                        Successfully loaded {categoriesData?.total} predefined categories
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Jobs with Categories Test */}
          <Card className="glass border-0 rounded-3xl">
            <CardHeader className="bg-gradient-to-r from-green-500/5 to-blue-500/5 border-b border-border/50">
              <CardTitle>💼 Jobs with Categories</CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              {jobsData ? (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="font-semibold text-foreground">Sample Jobs:</span>
                    <Badge variant="secondary" className="rounded-full">
                      {jobsData.jobs.length} jobs
                    </Badge>
                  </div>

                  <div className="space-y-3">
                    {jobsData.jobs.map((job) => (
                      <div
                        key={job.id}
                        className="p-4 bg-muted/30 rounded-2xl space-y-2"
                      >
                        <div className="flex items-start justify-between">
                          <h5 className="font-semibold text-foreground text-sm">{job.title}</h5>
                          <Badge className="rounded-full text-xs">
                            {job.category}
                          </Badge>
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {job.companyName} • {job.location}
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="pt-4 border-t border-border/50">
                    <div className="text-center">
                      <div className="text-green-600 font-semibold mb-2">✅ Job Categories Working!</div>
                      <p className="text-muted-foreground text-sm">
                        Jobs are properly categorized and displayed
                      </p>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  <span className="ml-3 text-muted-foreground">Loading jobs...</span>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Test Links */}
        <Card className="glass border-0 rounded-3xl">
          <CardContent className="p-6">
            <h3 className="font-bold text-foreground mb-4">🧪 Test the Implementation:</h3>
            <div className="grid md:grid-cols-3 gap-4">
              <Button asChild className="rounded-2xl font-bold h-12">
                <Link href="/jobs">
                  Test Job Filtering
                </Link>
              </Button>
              <Button asChild variant="outline" className="rounded-2xl font-bold h-12">
                <Link href="http://localhost:5174/jobs/new" target="_blank">
                  Test Admin Job Creation
                </Link>
              </Button>
              <Button asChild variant="outline" className="rounded-2xl font-bold h-12">
                <Link href="http://localhost:5000/api-docs" target="_blank">
                  View API Documentation
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Status Summary */}
        <Card className="glass border-0 rounded-3xl">
          <CardContent className="p-6">
            <h3 className="font-bold text-foreground mb-4">📊 Implementation Status:</h3>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <h4 className="font-semibold text-foreground">✅ Backend Features:</h4>
                <ul className="space-y-2 text-sm text-muted-foreground">
                  <li>• 25 Predefined Categories</li>
                  <li>• Categories API Endpoint</li>
                  <li>• Job Category Validation</li>
                  <li>• Swagger Documentation</li>
                </ul>
              </div>
              <div className="space-y-3">
                <h4 className="font-semibold text-foreground">✅ Frontend Features:</h4>
                <ul className="space-y-2 text-sm text-muted-foreground">
                  <li>• Category Filtering UI</li>
                  <li>• Admin Category Dropdown</li>
                  <li>• Real-time Category Loading</li>
                  <li>• Modern Gen Z Design</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
