import { Request, Response, NextFunction } from 'express';
import { db } from '../lib/db';
import { hashPassword, comparePassword } from '../lib/utils';
import { generateToken } from '../lib/jwt';
import {
  registerSchema,
  loginSchema,
  sendVerificationSchema,
  verifyEmailSchema,
  forgotPasswordSchema,
  resetPasswordSchema
} from '../schemas/auth';
import { createError } from '../middleware/errorHandler';
import {
  generateToken as generateEmailToken,
  sendVerificationEmail,
  sendPasswordResetEmail
} from '../lib/email';

export const authController = {
  // User Registration
  register: async (req: Request, res: Response, next: NextFunction) => {
    try {
      const validatedData = registerSchema.parse(req.body);

      // Check if user already exists
      const existingUser = await db.user.findUnique({
        where: { email: validatedData.email }
      });

      if (existingUser) {
        throw createError('User already exists with this email', 409);
      }

      // Hash password
      const hashedPassword = await hashPassword(validatedData.password);

      // Generate email verification token
      const verificationToken = generateEmailToken();
      const verificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

      // Parse full name into first and last name
      const nameParts = validatedData.fullName.trim().split(' ');
      const firstName = nameParts[0];
      const lastName = nameParts.length > 1 ? nameParts.slice(1).join(' ') : '';

      // Create user
      const user = await db.user.create({
        data: {
          email: validatedData.email,
          password: hashedPassword,
          firstName: firstName,
          lastName: lastName,
          phone: validatedData.phone,
          experienceLevel: validatedData.experienceLevel,
          emailVerificationToken: verificationToken,
          emailVerificationExpires: verificationExpires,
        },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          phone: true,
          experienceLevel: true,
          role: true,
          profileCompleted: true,
          emailVerified: true,
          createdAt: true,
        }
      });

      // Send verification email
      const emailSent = await sendVerificationEmail(user.email, verificationToken);

      res.status(201).json({
        message: 'User registered successfully. Please check your email to verify your account.',
        user,
        emailSent,
        requiresVerification: true,
      });
    } catch (error) {
      next(error);
    }
  },

  // User Login
  login: async (req: Request, res: Response, next: NextFunction) => {
    try {
      const validatedData = loginSchema.parse(req.body);

      // Find user
      const user = await db.user.findUnique({
        where: { email: validatedData.email }
      });

      if (!user) {
        throw createError('Invalid email or password', 401);
      }

      // Check password
      const isPasswordValid = await comparePassword(validatedData.password, user.password);

      if (!isPasswordValid) {
        throw createError('Invalid email or password', 401);
      }

      // Check if email is verified
      if (!user.emailVerified) {
        throw createError('Please verify your email address before logging in. Check your inbox for the verification link.', 403);
      }

      // Generate JWT token
      const token = generateToken({
        userId: user.id,
        email: user.email,
        role: user.role,
      });

      // Return user data without password
      const { password, ...userWithoutPassword } = user;

      res.json({
        message: 'Login successful',
        user: userWithoutPassword,
        token,
      });
    } catch (error) {
      next(error);
    }
  },

  // Get Current User
  getMe: async (req: Request, res: Response, next: NextFunction) => {
    try {
      const user = await db.user.findUnique({
        where: { id: req.user!.id },
        select: {
          id: true,
          email: true,
          phone: true,
          experienceLevel: true,
          role: true,
          emailVerified: true,
          firstName: true,
          lastName: true,
          profilePicture: true,
          resume: true,
          bio: true,
          skills: true,
          experience: true,
          education: true,
          location: true,
          website: true,
          linkedin: true,
          github: true,
          profileCompleted: true,
          createdAt: true,
          updatedAt: true,
        }
      });

      if (!user) {
        throw createError('User not found', 404);
      }

      res.json({ user });
    } catch (error) {
      next(error);
    }
  },

  // Logout
  logout: (req: Request, res: Response) => {
    res.json({ message: 'Logout successful' });
  },

  // Admin Registration
  adminRegister: async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { email, password, name, secretKey } = req.body;

      // Validate required fields
      if (!email || !password || !name || !secretKey) {
        throw createError('All fields are required', 400);
      }

      // Check secret key
      const ADMIN_SECRET_KEY = process.env.ADMIN_SECRET_KEY || 'admin-secret-2024';
      if (secretKey !== ADMIN_SECRET_KEY) {
        throw createError('Invalid secret key', 401);
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        throw createError('Invalid email format', 400);
      }

      // Validate password length
      if (password.length < 8) {
        throw createError('Password must be at least 8 characters long', 400);
      }

      // Check if admin already exists
      const existingAdmin = await db.user.findUnique({
        where: { email }
      });

      if (existingAdmin) {
        throw createError('Admin already exists with this email', 409);
      }

      // Hash password
      const hashedPassword = await hashPassword(password);

      // Parse full name into first and last name
      const nameParts = name.trim().split(' ');
      const firstName = nameParts[0];
      const lastName = nameParts.length > 1 ? nameParts.slice(1).join(' ') : '';

      // Create admin user
      const admin = await db.user.create({
        data: {
          email,
          password: hashedPassword,
          firstName: firstName,
          lastName: lastName,
          role: 'ADMIN',
          profileCompleted: true,
        },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          role: true,
          createdAt: true,
        }
      });

      // Generate JWT token
      const token = generateToken({
        userId: admin.id,
        email: admin.email,
        role: admin.role,
      });

      res.status(201).json({
        message: 'Admin registered successfully',
        admin,
        token,
      });
    } catch (error) {
      next(error);
    }
  },

  // Admin Login
  adminLogin: async (req: Request, res: Response, next: NextFunction) => {
    try {
      const validatedData = loginSchema.parse(req.body);

      // Find admin user
      const admin = await db.user.findUnique({
        where: {
          email: validatedData.email,
        }
      });

      if (!admin || admin.role !== 'ADMIN') {
        throw createError('Invalid email or password', 401);
      }

      // Check password
      const isPasswordValid = await comparePassword(validatedData.password, admin.password);

      if (!isPasswordValid) {
        throw createError('Invalid email or password', 401);
      }

      // Generate JWT token
      const token = generateToken({
        userId: admin.id,
        email: admin.email,
        role: admin.role,
      });

      // Return admin data without password
      const { password, ...adminWithoutPassword } = admin;

      res.json({
        message: 'Admin login successful',
        admin: adminWithoutPassword,
        token,
      });
    } catch (error) {
      next(error);
    }
  },

  // Get Admin Profile
  getAdminProfile: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // Check if user is admin
      if (req.user!.role !== 'ADMIN') {
        throw createError('Admin access required', 403);
      }

      const admin = await db.user.findUnique({
        where: { id: req.user!.id },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          role: true,
          createdAt: true,
          updatedAt: true,
        }
      });

      if (!admin) {
        throw createError('Admin not found', 404);
      }

      res.json({ admin });
    } catch (error) {
      next(error);
    }
  },

  // Send Email Verification
  sendVerification: async (req: Request, res: Response, next: NextFunction) => {
    try {
      const validatedData = sendVerificationSchema.parse(req.body);

      // Find user
      const user = await db.user.findUnique({
        where: { email: validatedData.email }
      });

      if (!user) {
        throw createError('User not found', 404);
      }

      if (user.emailVerified) {
        throw createError('Email is already verified', 400);
      }

      // Generate new verification token
      const verificationToken = generateEmailToken();
      const verificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

      // Update user with new token
      await db.user.update({
        where: { id: user.id },
        data: {
          emailVerificationToken: verificationToken,
          emailVerificationExpires: verificationExpires,
        }
      });

      // Send verification email
      const emailSent = await sendVerificationEmail(user.email, verificationToken);

      res.json({
        message: 'Verification email sent successfully',
        emailSent,
      });
    } catch (error) {
      next(error);
    }
  },

  // Verify Email
  verifyEmail: async (req: Request, res: Response, next: NextFunction) => {
    try {
      const validatedData = verifyEmailSchema.parse(req.body);

      // Find user with verification token
      const user = await db.user.findUnique({
        where: { emailVerificationToken: validatedData.token }
      });

      if (!user) {
        throw createError('Invalid or expired verification token', 400);
      }

      // Check if token is expired
      if (user.emailVerificationExpires && user.emailVerificationExpires < new Date()) {
        throw createError('Verification token has expired', 400);
      }

      // Update user as verified
      const updatedUser = await db.user.update({
        where: { id: user.id },
        data: {
          emailVerified: true,
          emailVerificationToken: null,
          emailVerificationExpires: null,
        },
        select: {
          id: true,
          email: true,
          emailVerified: true,
          role: true,
        }
      });

      // Generate JWT token for automatic login
      const token = generateToken({
        userId: updatedUser.id,
        email: updatedUser.email,
        role: updatedUser.role,
      });

      res.json({
        message: 'Email verified successfully',
        user: updatedUser,
        token,
      });
    } catch (error) {
      next(error);
    }
  },

  // Forgot Password
  forgotPassword: async (req: Request, res: Response, next: NextFunction) => {
    try {
      const validatedData = forgotPasswordSchema.parse(req.body);

      // Find user
      const user = await db.user.findUnique({
        where: { email: validatedData.email }
      });

      if (!user) {
        // Don't reveal if user exists or not for security
        res.json({
          message: 'If an account with that email exists, we have sent a password reset link.',
        });
        return;
      }

      // Generate password reset token
      const resetToken = generateEmailToken();
      const resetExpires = new Date(Date.now() + 60 * 60 * 1000); // 1 hour

      // Update user with reset token
      await db.user.update({
        where: { id: user.id },
        data: {
          passwordResetToken: resetToken,
          passwordResetExpires: resetExpires,
        }
      });

      // Send password reset email
      const emailSent = await sendPasswordResetEmail(user.email, resetToken);

      res.json({
        message: 'If an account with that email exists, we have sent a password reset link.',
        emailSent,
      });
    } catch (error) {
      next(error);
    }
  },

  // Reset Password
  resetPassword: async (req: Request, res: Response, next: NextFunction) => {
    try {
      const validatedData = resetPasswordSchema.parse(req.body);

      // Find user with reset token
      const user = await db.user.findUnique({
        where: { passwordResetToken: validatedData.token }
      });

      if (!user) {
        throw createError('Invalid or expired reset token', 400);
      }

      // Check if token is expired
      if (user.passwordResetExpires && user.passwordResetExpires < new Date()) {
        throw createError('Reset token has expired', 400);
      }

      // Hash new password
      const hashedPassword = await hashPassword(validatedData.password);

      // Update user with new password and clear reset token
      const updatedUser = await db.user.update({
        where: { id: user.id },
        data: {
          password: hashedPassword,
          passwordResetToken: null,
          passwordResetExpires: null,
        },
        select: {
          id: true,
          email: true,
          role: true,
        }
      });

      // Generate JWT token for automatic login
      const token = generateToken({
        userId: updatedUser.id,
        email: updatedUser.email,
        role: updatedUser.role,
      });

      res.json({
        message: 'Password reset successfully',
        user: updatedUser,
        token,
      });
    } catch (error) {
      next(error);
    }
  }
};
